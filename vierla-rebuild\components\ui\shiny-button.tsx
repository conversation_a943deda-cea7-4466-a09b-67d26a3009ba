import React, { CSSProperties } from "react";
import { cn } from "@/lib/utils";

export interface ShimmerButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  shimmerColor?: string;
  shimmerSize?: string;
  borderRadius?: string;
  shimmerDuration?: string;
  background?: string;
  className?: string;
  children?: React.ReactNode;
  asChild?: boolean;
}

const ShinyButton = React.forwardRef<HTMLButtonElement, ShimmerButtonProps>(
  (
    {
      shimmerColor = "#ffffff",
      shimmerSize = "0.05em",
      shimmerDuration = "3s",
      borderRadius = "100px",
      background = "rgba(0, 0, 0, 1)",
      className,
      children,
      asChild,
      ...props
    },
    ref,
  ) => {
    if (asChild) {
      return (
        <div
          style={
            {
              "--spread": "90deg",
              "--shimmer-color": shimmerColor,
              "--radius": borderRadius,
              "--speed": shimmerDuration,
              "--cut": shimmerSize,
              "--bg": background,
            } as CSSProperties
          }
          className={cn(
            "group relative z-0 flex cursor-pointer items-center justify-center overflow-hidden whitespace-nowrap p-[2px] [border-radius:var(--radius)]",
            "transform-gpu transition-transform duration-300 ease-in-out active:translate-y-px",
            "min-h-[44px]", // Ensure minimum height for proper shimmer effect
            className,
          )}
        >
          {/* shimmer background layer */}
          <div
            className={cn(
              "absolute inset-0 overflow-visible [container-type:size] rounded-[inherit]",
            )}
          >
            {/* shimmer effect */}
            <div className="absolute inset-0 h-[100cqh] animate-shimmer-slide [aspect-ratio:1] [border-radius:0] [mask:none]">
              <div className="animate-spin-around absolute -inset-full w-auto rotate-0 [background:conic-gradient(from_calc(270deg-(var(--spread)*0.5)),transparent_0,var(--shimmer-color)_var(--spread),transparent_var(--spread))] [translate:0_0]" />
            </div>
          </div>

          {/* top layer with #B8956A background */}
          <div
            className={cn(
              "relative z-10 flex items-center justify-center px-6 py-3 text-white rounded-[calc(var(--radius)-2px)]",
              "shadow-[inset_0_-8px_10px_#ffffff1f]",
              "transform-gpu transition-all duration-300 ease-in-out",
              "group-hover:shadow-[inset_0_-6px_10px_#ffffff3f]",
              "group-active:shadow-[inset_0_-10px_10px_#ffffff3f]",
            )}
            style={{ backgroundColor: '#B8956A' }}
          >
            {children}
          </div>
        </div>
      );
    }

    return (
      <button
        style={
          {
            "--spread": "90deg",
            "--shimmer-color": shimmerColor,
            "--radius": borderRadius,
            "--speed": shimmerDuration,
            "--cut": shimmerSize,
            "--bg": background,
          } as CSSProperties
        }
        className={cn(
          "group relative z-0 flex cursor-pointer items-center justify-center overflow-hidden p-[2px] [border-radius:var(--radius)]",
          "transform-gpu transition-transform duration-300 ease-in-out active:translate-y-px",
          "min-h-[44px]", // Ensure minimum height for proper shimmer effect
          // Remove whitespace-nowrap to allow text wrapping for longer content
          className,
        )}
        ref={ref}
        {...props}
      >
        {/* shimmer background layer */}
        <div
          className={cn(
            "absolute inset-0 overflow-visible [container-type:size] rounded-[inherit]",
          )}
        >
          {/* shimmer effect */}
          <div className="absolute inset-0 h-[100cqh] animate-shimmer-slide [aspect-ratio:1] [border-radius:0] [mask:none]">
            <div className="animate-spin-around absolute -inset-full w-auto rotate-0 [background:conic-gradient(from_calc(270deg-(var(--spread)*0.5)),transparent_0,var(--shimmer-color)_var(--spread),transparent_var(--spread))] [translate:0_0]" />
          </div>
        </div>

        {/* top layer with #B8956A background */}
        <div
          className={cn(
            "relative z-10 flex items-center justify-center text-white rounded-[calc(var(--radius)-2px)] w-full h-full",
            "shadow-[inset_0_-8px_10px_#ffffff1f]",
            "transform-gpu transition-all duration-300 ease-in-out",
            "group-hover:shadow-[inset_0_-6px_10px_#ffffff3f]",
            "group-active:shadow-[inset_0_-10px_10px_#ffffff3f]",
            // Default padding that can be overridden
            "px-6 py-3",
          )}
          style={{ backgroundColor: '#B8956A' }}
        >
          {children}
        </div>
      </button>
    );
  },
);

ShinyButton.displayName = "ShinyButton";

export default ShinyButton;
