{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/background-beams.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const BackgroundBeams = registerClientReference(\n    function() { throw new Error(\"Attempted to call BackgroundBeams() from the server but BackgroundBeams is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/background-beams.tsx <module evaluation>\",\n    \"BackgroundBeams\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,oEACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/background-beams.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const BackgroundBeams = registerClientReference(\n    function() { throw new Error(\"Attempted to call BackgroundBeams() from the server but BackgroundBeams is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/background-beams.tsx\",\n    \"BackgroundBeams\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,gDACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/interactive-hover-button.tsx"], "sourcesContent": ["import React from \"react\";\nimport { ArrowRight } from \"lucide-react\";\nimport { cn } from \"@/lib/utils\";\n\ninterface InteractiveHoverButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  text?: string;\n  asChild?: boolean;\n}\n\nconst InteractiveHoverButton = React.forwardRef<\n  HTMLButtonElement,\n  InteractiveHoverButtonProps\n>(({ text = \"Button\", className, asChild, ...props }, ref) => {\n  if (asChild) {\n    return (\n      <div\n        className={cn(\n          \"group relative w-32 cursor-pointer overflow-hidden rounded-full border border-primary/30 bg-primary/10 p-2 text-center font-semibold text-white drop-shadow-lg\",\n          className,\n        )}\n      >\n        <span className=\"inline-block translate-x-1 transition-all duration-300 group-hover:translate-x-12 group-hover:opacity-0\">\n          {text}\n        </span>\n        <div className=\"absolute top-0 z-10 flex h-full w-full translate-x-12 items-center justify-center gap-2 text-white opacity-0 transition-all duration-300 group-hover:-translate-x-1 group-hover:opacity-100\">\n          <span>{text}</span>\n          <ArrowRight />\n        </div>\n        <div className=\"absolute left-[20%] top-[40%] h-2 w-2 scale-[1] rounded-lg bg-primary transition-all duration-300 group-hover:left-[0%] group-hover:top-[0%] group-hover:h-full group-hover:w-full group-hover:scale-[1.8] group-hover:bg-primary\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <button\n      ref={ref}\n      className={cn(\n        \"group relative w-32 cursor-pointer overflow-hidden rounded-full border border-primary/30 bg-primary/10 p-2 text-center font-semibold text-white drop-shadow-lg\",\n        className,\n      )}\n      {...props}\n    >\n      <span className=\"inline-block translate-x-1 transition-all duration-300 group-hover:translate-x-12 group-hover:opacity-0\">\n        {text}\n      </span>\n      <div className=\"absolute top-0 z-10 flex h-full w-full translate-x-12 items-center justify-center gap-2 text-white opacity-0 transition-all duration-300 group-hover:-translate-x-1 group-hover:opacity-100\">\n        <span>{text}</span>\n        <ArrowRight />\n      </div>\n      <div className=\"absolute left-[20%] top-[40%] h-2 w-2 scale-[1] rounded-lg bg-primary transition-all duration-300 group-hover:left-[0%] group-hover:top-[0%] group-hover:h-full group-hover:w-full group-hover:scale-[1.8] group-hover:bg-primary\"></div>\n    </button>\n  );\n});\n\nInteractiveHoverButton.displayName = \"InteractiveHoverButton\";\n\nexport { InteractiveHoverButton };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAQA,MAAM,uCAAyB,qMAAA,CAAA,UAAK,CAAC,UAAU,CAG7C,CAAC,EAAE,OAAO,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE;IACpD,IAAI,SAAS;QACX,qBACE,8OAAC;YACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kKACA;;8BAGF,8OAAC;oBAAK,WAAU;8BACb;;;;;;8BAEH,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;sCAAM;;;;;;sCACP,8OAAC,kNAAA,CAAA,aAAU;;;;;;;;;;;8BAEb,8OAAC;oBAAI,WAAU;;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACb;;;;;;0BAEH,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCAAM;;;;;;kCACP,8OAAC,kNAAA,CAAA,aAAU;;;;;;;;;;;0BAEb,8OAAC;gBAAI,WAAU;;;;;;;;;;;;AAGrB;AAEA,uBAAuB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 163, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/app/pricing/page.tsx"], "sourcesContent": ["import { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { BackgroundBeams } from \"@/components/ui/background-beams\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { GlowingCardContainer } from \"@/components/ui/glowing-card\";\nimport ShinyButton from \"@/components/ui/shiny-button\";\nimport { InteractiveHoverButton } from \"@/components/ui/interactive-hover-button\";\nimport { Check, Star } from \"lucide-react\";\nimport Link from \"next/link\";\n\nexport default function Pricing() {\n  const plans = [\n    {\n      name: \"Starter\",\n      description: \"Perfect for getting started\",\n      price: \"Free\",\n      features: [\n        \"1 Project\",\n        \"Basic Website Builder\",\n        \"5 Invoices/month\",\n        \"Email Support\",\n        \"Basic Templates\"\n      ],\n      popular: false,\n      cta: \"Get Started Free\"\n    },\n    {\n      name: \"Pro\",\n      description: \"Most popular for growing businesses\",\n      price: \"$29/month\",\n      features: [\n        \"Unlimited Projects\",\n        \"Full Website Builder\",\n        \"Unlimited Invoicing\",\n        \"Integrated CRM\",\n        \"Advanced Analytics\",\n        \"Priority Support\",\n        \"Custom Domain\",\n        \"Premium Templates\"\n      ],\n      popular: true,\n      cta: \"Start Pro Trial\"\n    },\n    {\n      name: \"Business\",\n      description: \"For larger teams and enterprises\",\n      price: \"$79/month\",\n      features: [\n        \"Everything in Pro\",\n        \"Team Collaboration\",\n        \"White-label Options\",\n        \"API Access\",\n        \"Dedicated Support\",\n        \"Custom Integrations\",\n        \"Advanced Automation\"\n      ],\n      popular: false,\n      cta: \"Contact Sales\"\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen relative overflow-hidden\">\n      <BackgroundBeams />\n\n      {/* Hero Section */}\n      <section className=\"relative z-10 w-full px-4 py-20 pt-32\">\n        <div className=\"text-center max-w-6xl mx-auto\">\n          <h1 className=\"text-4xl md:text-6xl font-black mb-6 leading-none text-white drop-shadow-lg\">\n            Simple, Transparent Pricing\n          </h1>\n          <p className=\"text-xl md:text-2xl text-white/90 mb-8 leading-relaxed max-w-4xl mx-auto drop-shadow-sm\">\n            Choose the plan that fits your business needs. Start free and scale as you grow.\n          </p>\n        </div>\n      </section>\n\n      {/* Pricing Cards */}\n      <section className=\"relative z-10 py-20 border-t border-white/20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"grid gap-6 lg:grid-cols-3 max-w-6xl mx-auto\">\n            {plans.map((plan, index) => (\n              <div key={index} className=\"relative h-full\">\n                {/* Card container with hover group */}\n                <div className=\"relative h-full group/card\">\n                  {/* Golden glow effect only on card hover, excluding button area */}\n                  <div className=\"absolute -inset-0.5 rounded-lg bg-gradient-to-r from-[#B8956A]/30 via-[#B8956A]/60 to-[#B8956A]/30 opacity-0 group-hover/card:opacity-100 transition-opacity duration-500 blur-sm\" />\n\n                  <div className=\"relative h-full bg-white/10 backdrop-blur-md border border-white/20 shadow-xl rounded-2xl overflow-hidden\">\n                    <div className=\"relative h-full flex flex-col p-6\">\n                      <div className=\"text-center pb-8\">\n                        <h3 className=\"text-2xl font-bold text-white drop-shadow-lg mb-2\">{plan.name}</h3>\n                        <p className=\"text-white/80 drop-shadow-sm mb-4\">{plan.description}</p>\n                        <div className=\"mt-4\">\n                          <span className=\"text-4xl font-black text-white drop-shadow-lg\">{plan.price}</span>\n                        </div>\n                      </div>\n                      <div className=\"flex-grow flex flex-col\">\n                        <ul className=\"space-y-3 mb-8 flex-grow\">\n                          {plan.features.map((feature, idx) => (\n                            <li key={idx} className=\"flex items-center text-white/80 drop-shadow-sm\">\n                              <Check className=\"h-4 w-4 text-primary mr-3 flex-shrink-0\" />\n                              <span className=\"text-sm\">{feature}</span>\n                            </li>\n                          ))}\n                        </ul>\n                        {/* Button area with separate hover group */}\n                        <div className=\"mt-auto flex justify-center group/button\">\n                          <Link href={plan.name === \"Business\" ? \"/contact\" : \"/apply\"}>\n                            <InteractiveHoverButton\n                              text={plan.cta}\n                              className=\"w-40 bg-primary/20 border-primary/40 hover:bg-primary/30\"\n                            />\n                          </Link>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"relative z-10 py-20 border-t border-white/20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"mx-auto flex max-w-[58rem] flex-col items-center space-y-4 text-center\">\n            <h2 className=\"font-heading text-3xl leading-[1.1] sm:text-3xl md:text-6xl text-white drop-shadow-lg\">\n              Ready to get started?\n            </h2>\n            <p className=\"max-w-[42rem] leading-normal text-white/90 sm:text-xl sm:leading-8 drop-shadow-sm\">\n              Join thousands of entrepreneurs who trust Vierla to power their business.\n            </p>\n            <div className=\"space-x-4\">\n              <ShinyButton asChild>\n                <a href=\"/apply\">Start Free Trial</a>\n              </ShinyButton>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AAGA;AACA;AACA;AACA;;;;;;;AAEe,SAAS;IACtB,MAAM,QAAQ;QACZ;YACE,MAAM;YACN,aAAa;YACb,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;aACD;YACD,SAAS;YACT,KAAK;QACP;QACA;YACE,MAAM;YACN,aAAa;YACb,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,SAAS;YACT,KAAK;QACP;QACA;YACE,MAAM;YACN,aAAa;YACb,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,SAAS;YACT,KAAK;QACP;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,wIAAA,CAAA,kBAAe;;;;;0BAGhB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA8E;;;;;;sCAG5F,8OAAC;4BAAE,WAAU;sCAA0F;;;;;;;;;;;;;;;;;0BAO3G,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;gCAAgB,WAAU;0CAEzB,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;;;;;sDAEf,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAqD,KAAK,IAAI;;;;;;0EAC5E,8OAAC;gEAAE,WAAU;0EAAqC,KAAK,WAAW;;;;;;0EAClE,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAK,WAAU;8EAAiD,KAAK,KAAK;;;;;;;;;;;;;;;;;kEAG/E,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,oBAC3B,8OAAC;wEAAa,WAAU;;0FACtB,8OAAC,oMAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;0FACjB,8OAAC;gFAAK,WAAU;0FAAW;;;;;;;uEAFpB;;;;;;;;;;0EAOb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oEAAC,MAAM,KAAK,IAAI,KAAK,aAAa,aAAa;8EAClD,cAAA,8OAAC,mJAAA,CAAA,yBAAsB;wEACrB,MAAM,KAAK,GAAG;wEACd,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BA7BhB;;;;;;;;;;;;;;;;;;;;0BA4ClB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwF;;;;;;0CAGtG,8OAAC;gCAAE,WAAU;0CAAoF;;;;;;0CAGjG,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oIAAA,CAAA,UAAW;oCAAC,OAAO;8CAClB,cAAA,8OAAC;wCAAE,MAAK;kDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjC", "debugId": null}}]}