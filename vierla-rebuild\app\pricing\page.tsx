import { <PERSON><PERSON> } from "@/components/ui/button";
import { BackgroundBeams } from "@/components/ui/background-beams";
import { Badge } from "@/components/ui/badge";
import { GlowingCardContainer } from "@/components/ui/glowing-card";
import ShinyButton from "@/components/ui/shiny-button";
import { InteractiveHoverButton } from "@/components/ui/interactive-hover-button";
import { Check, Star } from "lucide-react";
import Link from "next/link";

export default function Pricing() {
  const plans = [
    {
      name: "Starter",
      description: "Perfect for getting started",
      price: "Free",
      features: [
        "1 Project",
        "Basic Website Builder",
        "5 Invoices/month",
        "Email Support",
        "Basic Templates"
      ],
      popular: false,
      cta: "Get Started Free"
    },
    {
      name: "Pro",
      description: "Most popular for growing businesses",
      price: "$29/month",
      features: [
        "Unlimited Projects",
        "Full Website Builder",
        "Unlimited Invoicing",
        "Integrated CRM",
        "Advanced Analytics",
        "Priority Support",
        "Custom Domain",
        "Premium Templates"
      ],
      popular: true,
      cta: "Start Pro Trial"
    },
    {
      name: "Business",
      description: "For larger teams and enterprises",
      price: "$79/month",
      features: [
        "Everything in Pro",
        "Team Collaboration",
        "White-label Options",
        "API Access",
        "Dedicated Support",
        "Custom Integrations",
        "Advanced Automation"
      ],
      popular: false,
      cta: "Contact Sales"
    }
  ];

  return (
    <div className="min-h-screen relative overflow-hidden">
      <BackgroundBeams />

      {/* Hero Section */}
      <section className="relative z-10 w-full px-4 py-20 pt-32">
        <div className="text-center max-w-6xl mx-auto">
          <h1 className="text-4xl md:text-6xl font-black mb-6 leading-none text-white drop-shadow-lg">
            Simple, Transparent Pricing
          </h1>
          <p className="text-xl md:text-2xl text-white/90 mb-8 leading-relaxed max-w-4xl mx-auto drop-shadow-sm">
            Choose the plan that fits your business needs. Start free and scale as you grow.
          </p>
        </div>
      </section>

      {/* Pricing Cards */}
      <section className="relative z-10 py-20 border-t border-white/20">
        <div className="container mx-auto px-4">
          <div className="grid gap-6 lg:grid-cols-3 max-w-6xl mx-auto">
            {plans.map((plan, index) => (
              <div key={index} className="relative h-full">
                {/* Card container with hover group */}
                <div className="relative h-full group/card">
                  {/* Golden glow effect only on card hover, excluding button area */}
                  <div className="absolute -inset-0.5 rounded-lg bg-gradient-to-r from-[#B8956A]/30 via-[#B8956A]/60 to-[#B8956A]/30 opacity-0 group-hover/card:opacity-100 transition-opacity duration-500 blur-sm" />

                  <div className="relative h-full bg-white/10 backdrop-blur-md border border-white/20 shadow-xl rounded-2xl overflow-hidden">
                    <div className="relative h-full flex flex-col p-6">
                      <div className="text-center pb-8">
                        <h3 className="text-2xl font-bold text-white drop-shadow-lg mb-2">{plan.name}</h3>
                        <p className="text-white/80 drop-shadow-sm mb-4">{plan.description}</p>
                        <div className="mt-4">
                          <span className="text-4xl font-black text-white drop-shadow-lg">{plan.price}</span>
                        </div>
                      </div>
                      <div className="flex-grow flex flex-col">
                        <ul className="space-y-3 mb-8 flex-grow">
                          {plan.features.map((feature, idx) => (
                            <li key={idx} className="flex items-center text-white/80 drop-shadow-sm">
                              <Check className="h-4 w-4 text-primary mr-3 flex-shrink-0" />
                              <span className="text-sm">{feature}</span>
                            </li>
                          ))}
                        </ul>
                        {/* Button area with separate hover group */}
                        <div className="mt-auto flex justify-center group/button">
                          <Link href={plan.name === "Business" ? "/contact" : "/apply"}>
                            <InteractiveHoverButton
                              text={plan.cta}
                              className="w-40 bg-primary/20 border-primary/40 hover:bg-primary/30"
                            />
                          </Link>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative z-10 py-20 border-t border-white/20">
        <div className="container mx-auto px-4">
          <div className="mx-auto flex max-w-[58rem] flex-col items-center space-y-4 text-center">
            <h2 className="font-heading text-3xl leading-[1.1] sm:text-3xl md:text-6xl text-white drop-shadow-lg">
              Ready to get started?
            </h2>
            <p className="max-w-[42rem] leading-normal text-white/90 sm:text-xl sm:leading-8 drop-shadow-sm">
              Join thousands of entrepreneurs who trust Vierla to power their business.
            </p>
            <div className="space-x-4">
              <ShinyButton asChild>
                <a href="/apply">Start Free Trial</a>
              </ShinyButton>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
