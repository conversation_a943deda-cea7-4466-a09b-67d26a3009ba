{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/background-beams.tsx"], "sourcesContent": ["\"use client\";\nimport React from \"react\";\nimport { motion } from \"framer-motion\";\nimport { cn } from \"@/lib/utils\";\n\nexport const BackgroundBeams = ({ className }: { className?: string }) => {\n  return (\n    <div\n      className={cn(\n        \"absolute top-0 left-0 w-full h-full -z-10\",\n        className\n      )}\n    >\n      <div className=\"relative w-full h-full overflow-hidden\">\n        <div className=\"absolute inset-0 bg-zinc-900\"></div>\n        <div className=\"absolute h-full w-full\">\n          {/* Placeholder for beam elements */}\n          {/* This component often requires more complex SVG/div structures for the beams themselves. */}\n          {/* The following is a simplified representation. Refer to the source for the full SVG implementation. */}\n          <div className=\"absolute top-0 left-1/4 h-full w-px bg-gradient-to-b from-transparent via-purple-500 to-transparent animate-[beam_8s_linear_infinite]\"></div>\n          <div className=\"absolute top-0 left-1/2 h-full w-px bg-gradient-to-b from-transparent via-purple-500 to-transparent animate-[beam_10s_linear_infinite_2s_]\"></div>\n          <div className=\"absolute top-0 left-3/4 h-full w-px bg-gradient-to-b from-transparent via-purple-500 to-transparent animate-[beam_9s_linear_infinite_1s_]\"></div>\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKO,MAAM,kBAAkB,CAAC,EAAE,SAAS,EAA0B;IACnE,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6CACA;kBAGF,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAI,WAAU;;sCAIb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKzB", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/shiny-button.tsx"], "sourcesContent": ["import React, { CSSProperties } from \"react\";\nimport { cn } from \"@/lib/utils\";\n\nexport interface ShimmerButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  shimmerColor?: string;\n  shimmerSize?: string;\n  borderRadius?: string;\n  shimmerDuration?: string;\n  background?: string;\n  className?: string;\n  children?: React.ReactNode;\n  asChild?: boolean;\n}\n\nconst ShinyButton = React.forwardRef<HTMLButtonElement, ShimmerButtonProps>(\n  (\n    {\n      shimmerColor = \"#ffffff\",\n      shimmerSize = \"0.05em\",\n      shimmerDuration = \"3s\",\n      borderRadius = \"100px\",\n      background = \"rgba(0, 0, 0, 1)\",\n      className,\n      children,\n      asChild,\n      ...props\n    },\n    ref,\n  ) => {\n    if (asChild) {\n      return (\n        <div\n          style={\n            {\n              \"--spread\": \"90deg\",\n              \"--shimmer-color\": shimmerColor,\n              \"--radius\": borderRadius,\n              \"--speed\": shimmerDuration,\n              \"--cut\": shimmerSize,\n              \"--bg\": background,\n            } as CSSProperties\n          }\n          className={cn(\n            \"group relative z-0 flex cursor-pointer items-center justify-center overflow-hidden whitespace-nowrap p-[2px] [border-radius:var(--radius)]\",\n            \"transform-gpu transition-transform duration-300 ease-in-out active:translate-y-px\",\n            \"min-h-[44px]\", // Ensure minimum height for proper shimmer effect\n            className,\n          )}\n        >\n          {/* shimmer background layer */}\n          <div\n            className={cn(\n              \"absolute inset-0 overflow-visible [container-type:size] rounded-[inherit]\",\n            )}\n          >\n            {/* shimmer effect */}\n            <div className=\"absolute inset-0 h-[100cqh] animate-shimmer-slide [aspect-ratio:1] [border-radius:0] [mask:none]\">\n              <div className=\"animate-spin-around absolute -inset-full w-auto rotate-0 [background:conic-gradient(from_calc(270deg-(var(--spread)*0.5)),transparent_0,var(--shimmer-color)_var(--spread),transparent_var(--spread))] [translate:0_0]\" />\n            </div>\n          </div>\n\n          {/* top layer with #B8956A background */}\n          <div\n            className={cn(\n              \"relative z-10 flex items-center justify-center px-6 py-3 text-white rounded-[calc(var(--radius)-2px)]\",\n              \"shadow-[inset_0_-8px_10px_#ffffff1f]\",\n              \"transform-gpu transition-all duration-300 ease-in-out\",\n              \"group-hover:shadow-[inset_0_-6px_10px_#ffffff3f]\",\n              \"group-active:shadow-[inset_0_-10px_10px_#ffffff3f]\",\n            )}\n            style={{ backgroundColor: '#B8956A' }}\n          >\n            {children}\n          </div>\n        </div>\n      );\n    }\n\n    return (\n      <button\n        style={\n          {\n            \"--spread\": \"90deg\",\n            \"--shimmer-color\": shimmerColor,\n            \"--radius\": borderRadius,\n            \"--speed\": shimmerDuration,\n            \"--cut\": shimmerSize,\n            \"--bg\": background,\n          } as CSSProperties\n        }\n        className={cn(\n          \"group relative z-0 flex cursor-pointer items-center justify-center overflow-hidden p-[2px] [border-radius:var(--radius)]\",\n          \"transform-gpu transition-transform duration-300 ease-in-out active:translate-y-px\",\n          \"min-h-[44px]\", // Ensure minimum height for proper shimmer effect\n          // Remove whitespace-nowrap to allow text wrapping for longer content\n          className,\n        )}\n        ref={ref}\n        {...props}\n      >\n        {/* shimmer background layer */}\n        <div\n          className={cn(\n            \"absolute inset-0 overflow-visible [container-type:size] rounded-[inherit]\",\n          )}\n        >\n          {/* shimmer effect */}\n          <div className=\"absolute inset-0 h-[100cqh] animate-shimmer-slide [aspect-ratio:1] [border-radius:0] [mask:none]\">\n            <div className=\"animate-spin-around absolute -inset-full w-auto rotate-0 [background:conic-gradient(from_calc(270deg-(var(--spread)*0.5)),transparent_0,var(--shimmer-color)_var(--spread),transparent_var(--spread))] [translate:0_0]\" />\n          </div>\n        </div>\n\n        {/* top layer with #B8956A background */}\n        <div\n          className={cn(\n            \"relative z-10 flex items-center justify-center text-white rounded-[calc(var(--radius)-2px)] w-full h-full\",\n            \"shadow-[inset_0_-8px_10px_#ffffff1f]\",\n            \"transform-gpu transition-all duration-300 ease-in-out\",\n            \"group-hover:shadow-[inset_0_-6px_10px_#ffffff3f]\",\n            \"group-active:shadow-[inset_0_-10px_10px_#ffffff3f]\",\n            // Default padding that can be overridden\n            \"px-6 py-3\",\n          )}\n          style={{ backgroundColor: '#B8956A' }}\n        >\n          {children}\n        </div>\n      </button>\n    );\n  },\n);\n\nShinyButton.displayName = \"ShinyButton\";\n\nexport default ShinyButton;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAcA,MAAM,4BAAc,qMAAA,CAAA,UAAK,CAAC,UAAU,CAClC,CACE,EACE,eAAe,SAAS,EACxB,cAAc,QAAQ,EACtB,kBAAkB,IAAI,EACtB,eAAe,OAAO,EACtB,aAAa,kBAAkB,EAC/B,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OACJ,EACD;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YACC,OACE;gBACE,YAAY;gBACZ,mBAAmB;gBACnB,YAAY;gBACZ,WAAW;gBACX,SAAS;gBACT,QAAQ;YACV;YAEF,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8IACA,qFACA,gBACA;;8BAIF,8OAAC;oBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV;8BAIF,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;8BAKnB,8OAAC;oBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yGACA,wCACA,yDACA,oDACA;oBAEF,OAAO;wBAAE,iBAAiB;oBAAU;8BAEnC;;;;;;;;;;;;IAIT;IAEA,qBACE,8OAAC;QACC,OACE;YACE,YAAY;YACZ,mBAAmB;YACnB,YAAY;YACZ,WAAW;YACX,SAAS;YACT,QAAQ;QACV;QAEF,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,4HACA,qFACA,gBACA,qEAAqE;QACrE;QAEF,KAAK;QACJ,GAAG,KAAK;;0BAGT,8OAAC;gBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV;0BAIF,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;0BAKnB,8OAAC;gBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6GACA,wCACA,yDACA,oDACA,sDACA,yCAAyC;gBACzC;gBAEF,OAAO;oBAAE,iBAAiB;gBAAU;0BAEnC;;;;;;;;;;;;AAIT;AAGF,YAAY,WAAW,GAAG;uCAEX", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/new-shiny-button.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { motion, type AnimationProps } from \"framer-motion\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst animationProps: AnimationProps = {\n  initial: { \"--x\": \"100%\", scale: 0.8 },\n  animate: { \"--x\": \"-100%\", scale: 1 },\n  whileTap: { scale: 0.95 },\n  transition: {\n    repeat: Infinity,\n    repeatType: \"loop\",\n    repeatDelay: 1,\n    type: \"spring\",\n    stiffness: 20,\n    damping: 15,\n    mass: 2,\n    scale: {\n      type: \"spring\",\n      stiffness: 200,\n      damping: 5,\n      mass: 0.5,\n    },\n  },\n};\n\ninterface NewShinyButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const NewShinyButton: React.FC<NewShinyButtonProps> = ({\n  children,\n  className,\n  ...props\n}) => {\n  return (\n    <motion.button\n      {...animationProps}\n      {...props}\n      className={cn(\n        \"relative rounded-lg px-6 py-2 font-medium backdrop-blur-xl transition-shadow duration-300 ease-in-out hover:shadow\",\n        \"bg-[#B8956A] text-white drop-shadow-lg\",\n        \"dark:bg-[#B8956A] dark:hover:shadow-[0_0_20px_rgba(184,149,106,0.3)]\",\n        // Remove whitespace-nowrap to allow text wrapping for longer content\n        \"flex items-center justify-center text-center\",\n        className\n      )}\n      style={{\n        \"--primary\": \"184 149 106\", // #B8956A in RGB\n      } as React.CSSProperties}\n    >\n      <span\n        className=\"relative block size-full text-sm uppercase tracking-wide text-white font-medium\"\n        style={{\n          maskImage:\n            \"linear-gradient(-75deg,rgb(184,149,106) calc(var(--x) + 20%),transparent calc(var(--x) + 30%),rgb(184,149,106) calc(var(--x) + 100%))\",\n        }}\n      >\n        {children}\n      </span>\n      <span\n        style={{\n          mask: \"linear-gradient(rgb(0,0,0), rgb(0,0,0)) content-box,linear-gradient(rgb(0,0,0), rgb(0,0,0))\",\n          maskComposite: \"exclude\",\n        }}\n        className=\"absolute inset-0 z-10 block rounded-[inherit] bg-[linear-gradient(-75deg,rgba(184,149,106,0.1)_calc(var(--x)+20%),rgba(184,149,106,0.5)_calc(var(--x)+25%),rgba(184,149,106,0.1)_calc(var(--x)+100%))] p-px\"\n      ></span>\n    </motion.button>\n  );\n};\n\nexport default NewShinyButton;\n"], "names": [], "mappings": ";;;;;AAGA;AAEA;AALA;;;;AAOA,MAAM,iBAAiC;IACrC,SAAS;QAAE,OAAO;QAAQ,OAAO;IAAI;IACrC,SAAS;QAAE,OAAO;QAAS,OAAO;IAAE;IACpC,UAAU;QAAE,OAAO;IAAK;IACxB,YAAY;QACV,QAAQ;QACR,YAAY;QACZ,aAAa;QACb,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;QACN,OAAO;YACL,MAAM;YACN,WAAW;YACX,SAAS;YACT,MAAM;QACR;IACF;AACF;AAQO,MAAM,iBAAgD,CAAC,EAC5D,QAAQ,EACR,SAAS,EACT,GAAG,OACJ;IACC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACX,GAAG,cAAc;QACjB,GAAG,KAAK;QACT,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sHACA,0CACA,wEACA,qEAAqE;QACrE,gDACA;QAEF,OAAO;YACL,aAAa;QACf;;0BAEA,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,WACE;gBACJ;0BAEC;;;;;;0BAEH,8OAAC;gBACC,OAAO;oBACL,MAAM;oBACN,eAAe;gBACjB;gBACA,WAAU;;;;;;;;;;;;AAIlB;uCAEe", "debugId": null}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/app/contact/page.tsx"], "sourcesContent": ["\"use client\"\nimport { useState } from 'react'\nimport Link from \"next/link\"\nimport { BackgroundBeams } from \"@/components/ui/background-beams\";\nimport ShinyButton from \"@/components/ui/shiny-button\";\nimport { NewShinyButton } from \"@/components/ui/new-shiny-button\";\n\nexport default function ContactPage() {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    subject: '',\n    message: '',\n    type: 'general'\n  })\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  const [isSubmitted, setIsSubmitted] = useState(false)\n  const [error, setError] = useState('')\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setIsSubmitting(true)\n    setError('')\n\n    try {\n      // Submit to backend API with fallback to localStorage\n      const response = await fetch('/api/contact', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          ...formData,\n          timestamp: new Date().toISOString()\n        }),\n      })\n\n      const result = await response.json()\n\n      if (result.success) {\n        setIsSubmitted(true)\n        setFormData({ name: '', email: '', subject: '', message: '', type: 'general' })\n      } else {\n        setError(result.error || 'Something went wrong. Please try again.')\n      }\n    } catch (err) {\n      // Fallback to localStorage\n      const existingContacts = JSON.parse(localStorage.getItem('vierla-contacts') || '[]')\n      existingContacts.push({\n        ...formData,\n        timestamp: new Date().toISOString()\n      })\n      localStorage.setItem('vierla-contacts', JSON.stringify(existingContacts))\n      setIsSubmitted(true)\n    } finally {\n      setIsSubmitting(false)\n    }\n  }\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { name, value } = e.target\n    setFormData(prev => ({ ...prev, [name]: value }))\n  }\n\n  return (\n    <div className=\"min-h-screen relative overflow-hidden\">\n      <BackgroundBeams />\n\n      {/* Main Content */}\n      <main className=\"relative z-10 w-full px-4 py-20 pt-32\">\n        <div className=\"max-w-6xl mx-auto\">\n          <div className=\"text-center mb-16\">\n            <h1 className=\"text-4xl md:text-6xl font-black text-white mb-6 drop-shadow-lg\">\n              Contact Us\n            </h1>\n            <p className=\"text-xl text-white/90 max-w-2xl mx-auto drop-shadow-sm\">\n              Have questions about our services? Want to join our platform as a beauty professional? We'd love to hear from you.\n            </p>\n          </div>\n\n          <div className=\"grid lg:grid-cols-2 gap-12\">\n            {/* Contact Form */}\n            <div className=\"bg-white/10 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/20\">\n              <h2 className=\"text-2xl font-bold text-white mb-6 drop-shadow-lg\">\n                Send us a message\n              </h2>\n              \n              {isSubmitted ? (\n                <div className=\"text-center py-8\">\n                  <div className=\"inline-flex items-center justify-center w-16 h-16 rounded-full mb-4 bg-primary/20\">\n                    <svg className=\"w-8 h-8 text-primary\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                    </svg>\n                  </div>\n                  <h3 className=\"text-xl font-bold text-white mb-2 drop-shadow-lg\">Message Sent!</h3>\n                  <p className=\"text-white/80 drop-shadow-sm\">Thank you for reaching out. We'll get back to you within 24 hours.</p>\n                </div>\n              ) : (\n                <form onSubmit={handleSubmit} className=\"space-y-6\">\n                  <div className=\"grid md:grid-cols-2 gap-4\">\n                    <div>\n                      <label className=\"block text-white/90 text-sm font-medium mb-2 drop-shadow-sm\">Name *</label>\n                      <input\n                        type=\"text\"\n                        name=\"name\"\n                        value={formData.name}\n                        onChange={handleInputChange}\n                        required\n                        className=\"w-full px-4 py-3 rounded-xl bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent backdrop-blur-sm\"\n                        placeholder=\"Your full name\"\n                      />\n                    </div>\n                    <div>\n                      <label className=\"block text-white/90 text-sm font-medium mb-2 drop-shadow-sm\">Email *</label>\n                      <input\n                        type=\"email\"\n                        name=\"email\"\n                        value={formData.email}\n                        onChange={handleInputChange}\n                        required\n                        className=\"w-full px-4 py-3 rounded-xl bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent backdrop-blur-sm\"\n                        placeholder=\"<EMAIL>\"\n                      />\n                    </div>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-white/90 text-sm font-medium mb-2 drop-shadow-sm\">Contact Type</label>\n                    <select\n                      name=\"type\"\n                      value={formData.type}\n                      onChange={handleInputChange}\n                      className=\"w-full px-4 py-3 rounded-xl bg-white/10 border border-white/20 text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent backdrop-blur-sm\"\n                    >\n                      <option value=\"general\" className=\"bg-gray-800\">General Inquiry</option>\n                      <option value=\"customer\" className=\"bg-gray-800\">Customer Support</option>\n                      <option value=\"professional\" className=\"bg-gray-800\">Professional Application</option>\n                      <option value=\"partnership\" className=\"bg-gray-800\">Partnership</option>\n                      <option value=\"support\" className=\"bg-gray-800\">Technical Support</option>\n                    </select>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-white/90 text-sm font-medium mb-2 drop-shadow-sm\">Subject *</label>\n                    <input\n                      type=\"text\"\n                      name=\"subject\"\n                      value={formData.subject}\n                      onChange={handleInputChange}\n                      required\n                      className=\"w-full px-4 py-3 rounded-xl bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent backdrop-blur-sm\"\n                      placeholder=\"What's this about?\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-white/90 text-sm font-medium mb-2 drop-shadow-sm\">Message *</label>\n                    <textarea\n                      name=\"message\"\n                      value={formData.message}\n                      onChange={handleInputChange}\n                      required\n                      rows={5}\n                      className=\"w-full px-4 py-3 rounded-xl bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent backdrop-blur-sm resize-none\"\n                      placeholder=\"Tell us more about your inquiry...\"\n                    />\n                  </div>\n\n                  {error && (\n                    <div className=\"p-4 rounded-xl bg-red-500/20 border border-red-500/30\">\n                      <p className=\"text-red-200 text-sm drop-shadow-sm\">{error}</p>\n                    </div>\n                  )}\n\n                  <NewShinyButton\n                    type=\"submit\"\n                    disabled={isSubmitting}\n                    className=\"w-full py-4 text-lg\"\n                  >\n                    {isSubmitting ? 'Sending...' : 'Send Message'}\n                  </NewShinyButton>\n                </form>\n              )}\n            </div>\n\n            {/* Contact Information */}\n            <div className=\"space-y-8\">\n              <div className=\"bg-white/10 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/20\">\n                <h3 className=\"text-xl font-bold text-white mb-6 drop-shadow-lg\">\n                  Get in Touch\n                </h3>\n                \n                <div className=\"space-y-6\">\n                  <div className=\"flex items-start space-x-4\">\n                    <div className=\"w-12 h-12 rounded-xl flex items-center justify-center bg-white/10 border border-white/20\">\n                      <svg className=\"w-6 h-6 text-white drop-shadow-lg\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                      </svg>\n                    </div>\n                    <div>\n                      <h3 className=\"text-lg font-semibold text-white mb-1 drop-shadow-lg\">Email</h3>\n                      <p className=\"text-white/80 drop-shadow-sm\"><EMAIL></p>\n                      <p className=\"text-white/60 text-sm drop-shadow-sm\">We typically respond within 24 hours</p>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-start space-x-4\">\n                    <div className=\"w-12 h-12 rounded-xl flex items-center justify-center bg-white/10 border border-white/20\">\n                      <svg className=\"w-6 h-6 text-white drop-shadow-lg\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                      </svg>\n                    </div>\n                    <div>\n                      <h3 className=\"text-lg font-semibold text-white mb-1 drop-shadow-lg\">Location</h3>\n                      <p className=\"text-white/80 drop-shadow-sm\">Toronto & Ottawa, Ontario</p>\n                      <p className=\"text-white/60 text-sm drop-shadow-sm\">Serving the Greater Toronto and Ottawa areas</p>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-start space-x-4\">\n                    <div className=\"w-12 h-12 rounded-xl flex items-center justify-center bg-white/10 border border-white/20\">\n                      <svg className=\"w-6 h-6 text-white drop-shadow-lg\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                      </svg>\n                    </div>\n                    <div>\n                      <h3 className=\"text-lg font-semibold text-white mb-1 drop-shadow-lg\">Response Time</h3>\n                      <p className=\"text-white/80 drop-shadow-sm\">Within 24 hours</p>\n                      <p className=\"text-white/60 text-sm drop-shadow-sm\">Monday to Friday, 9 AM - 6 PM EST</p>\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white/10 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/20\">\n                <h3 className=\"text-xl font-bold text-white mb-4 drop-shadow-lg\">\n                  For Beauty Professionals\n                </h3>\n                <p className=\"text-white/80 mb-4 drop-shadow-sm\">\n                  Interested in joining our platform? We're always looking for talented, licensed beauty professionals.\n                </p>\n                <Link href=\"/apply\">\n                  <ShinyButton className=\"w-full\">\n                    Apply Now\n                  </ShinyButton>\n                </Link>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AALA;;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;QACT,MAAM;IACR;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAChB,SAAS;QAET,IAAI;YACF,sDAAsD;YACtD,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,GAAG,QAAQ;oBACX,WAAW,IAAI,OAAO,WAAW;gBACnC;YACF;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,eAAe;gBACf,YAAY;oBAAE,MAAM;oBAAI,OAAO;oBAAI,SAAS;oBAAI,SAAS;oBAAI,MAAM;gBAAU;YAC/E,OAAO;gBACL,SAAS,OAAO,KAAK,IAAI;YAC3B;QACF,EAAE,OAAO,KAAK;YACZ,2BAA2B;YAC3B,MAAM,mBAAmB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,sBAAsB;YAC/E,iBAAiB,IAAI,CAAC;gBACpB,GAAG,QAAQ;gBACX,WAAW,IAAI,OAAO,WAAW;YACnC;YACA,aAAa,OAAO,CAAC,mBAAmB,KAAK,SAAS,CAAC;YACvD,eAAe;QACjB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;IACjD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,wIAAA,CAAA,kBAAe;;;;;0BAGhB,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAiE;;;;;;8CAG/E,8OAAC;oCAAE,WAAU;8CAAyD;;;;;;;;;;;;sCAKxE,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAoD;;;;;;wCAIjE,4BACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAAuB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC9E,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;8DAGzE,8OAAC;oDAAG,WAAU;8DAAmD;;;;;;8DACjE,8OAAC;oDAAE,WAAU;8DAA+B;;;;;;;;;;;iEAG9C,8OAAC;4CAAK,UAAU;4CAAc,WAAU;;8DACtC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAA8D;;;;;;8EAC/E,8OAAC;oEACC,MAAK;oEACL,MAAK;oEACL,OAAO,SAAS,IAAI;oEACpB,UAAU;oEACV,QAAQ;oEACR,WAAU;oEACV,aAAY;;;;;;;;;;;;sEAGhB,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAA8D;;;;;;8EAC/E,8OAAC;oEACC,MAAK;oEACL,MAAK;oEACL,OAAO,SAAS,KAAK;oEACrB,UAAU;oEACV,QAAQ;oEACR,WAAU;oEACV,aAAY;;;;;;;;;;;;;;;;;;8DAKlB,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA8D;;;;;;sEAC/E,8OAAC;4DACC,MAAK;4DACL,OAAO,SAAS,IAAI;4DACpB,UAAU;4DACV,WAAU;;8EAEV,8OAAC;oEAAO,OAAM;oEAAU,WAAU;8EAAc;;;;;;8EAChD,8OAAC;oEAAO,OAAM;oEAAW,WAAU;8EAAc;;;;;;8EACjD,8OAAC;oEAAO,OAAM;oEAAe,WAAU;8EAAc;;;;;;8EACrD,8OAAC;oEAAO,OAAM;oEAAc,WAAU;8EAAc;;;;;;8EACpD,8OAAC;oEAAO,OAAM;oEAAU,WAAU;8EAAc;;;;;;;;;;;;;;;;;;8DAIpD,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA8D;;;;;;sEAC/E,8OAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAO,SAAS,OAAO;4DACvB,UAAU;4DACV,QAAQ;4DACR,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAIhB,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAA8D;;;;;;sEAC/E,8OAAC;4DACC,MAAK;4DACL,OAAO,SAAS,OAAO;4DACvB,UAAU;4DACV,QAAQ;4DACR,MAAM;4DACN,WAAU;4DACV,aAAY;;;;;;;;;;;;gDAIf,uBACC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;kEAAuC;;;;;;;;;;;8DAIxD,8OAAC,2IAAA,CAAA,iBAAc;oDACb,MAAK;oDACL,UAAU;oDACV,WAAU;8DAET,eAAe,eAAe;;;;;;;;;;;;;;;;;;8CAOvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAmD;;;;;;8DAIjE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAI,WAAU;wEAAoC,MAAK;wEAAO,QAAO;wEAAe,SAAQ;kFAC3F,cAAA,8OAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;;;;;;8EAGzE,8OAAC;;sFACC,8OAAC;4EAAG,WAAU;sFAAuD;;;;;;sFACrE,8OAAC;4EAAE,WAAU;sFAA+B;;;;;;sFAC5C,8OAAC;4EAAE,WAAU;sFAAuC;;;;;;;;;;;;;;;;;;sEAIxD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAI,WAAU;wEAAoC,MAAK;wEAAO,QAAO;wEAAe,SAAQ;;0FAC3F,8OAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAa;gFAAG,GAAE;;;;;;0FACrE,8OAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAa;gFAAG,GAAE;;;;;;;;;;;;;;;;;8EAGzE,8OAAC;;sFACC,8OAAC;4EAAG,WAAU;sFAAuD;;;;;;sFACrE,8OAAC;4EAAE,WAAU;sFAA+B;;;;;;sFAC5C,8OAAC;4EAAE,WAAU;sFAAuC;;;;;;;;;;;;;;;;;;sEAIxD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAI,WAAU;wEAAoC,MAAK;wEAAO,QAAO;wEAAe,SAAQ;kFAC3F,cAAA,8OAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;;;;;;8EAGzE,8OAAC;;sFACC,8OAAC;4EAAG,WAAU;sFAAuD;;;;;;sFACrE,8OAAC;4EAAE,WAAU;sFAA+B;;;;;;sFAC5C,8OAAC;4EAAE,WAAU;sFAAuC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAM5D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAmD;;;;;;8DAGjE,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DAGjD,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DACT,cAAA,8OAAC,oIAAA,CAAA,UAAW;wDAAC,WAAU;kEAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWlD", "debugId": null}}]}