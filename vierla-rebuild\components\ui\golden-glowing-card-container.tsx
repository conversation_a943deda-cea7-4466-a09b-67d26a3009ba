"use client";

import React from "react";
import { cn } from "@/lib/utils";
import { GoldenGlowingEffect } from "./golden-glowing-effect";

interface GoldenGlowingCardContainerProps {
  children: React.ReactNode;
  className?: string;
}

export const GoldenGlowingCardContainer: React.FC<GoldenGlowingCardContainerProps> = ({
  children,
  className,
}) => {
  return (
    <div className={cn("relative h-full group", className)}>
      {/* Outer container with glowing effect */}
      <div className="relative h-full rounded-[1.25rem] border-[0.75px] border-white/20 p-2 md:rounded-[1.5rem] md:p-3">
        <GoldenGlowingEffect
          spread={40}
          glow={true}
          disabled={false}
          proximity={64}
          inactiveZone={0.01}
          borderWidth={3}
        />
        
        {/* Inner container with margin */}
        <div className="relative flex h-full flex-col justify-between gap-6 overflow-hidden rounded-xl border-[0.75px] bg-white/10 backdrop-blur-md p-6 shadow-sm">
          {children}
        </div>
      </div>
    </div>
  );
};

export default GoldenGlowingCardContainer;
