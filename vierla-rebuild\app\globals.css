@tailwind base;
@tailwind components;
@tailwind utilities;

/* iOS Safe Area Support & Accent Color Implementation */
:root {
  --safe-area-inset-top: env(safe-area-inset-top);
  --safe-area-inset-right: env(safe-area-inset-right);
  --safe-area-inset-bottom: env(safe-area-inset-bottom);
  --safe-area-inset-left: env(safe-area-inset-left);

  /* CSS Accent Color Implementation */
  /* Primary accent color - warm gold */
  accent-color: #B8956A;

  /* Custom accent color variables for consistent theming */
  --accent-primary: #B8956A;
  --accent-secondary: #F4F1E8;
  --accent-hover: #A67C52;
  --accent-light: rgba(184, 149, 106, 0.1);
  --accent-medium: rgba(184, 149, 106, 0.3);
  --accent-strong: rgba(184, 149, 106, 0.8);
}

/* Comprehensive mobile background fix using modern viewport units */
html {
  background-color: #364035 !important;
  background: linear-gradient(to bottom right, #364035, #8B9A8C, #364035) !important;
  background-size: 100% 100% !important;
  background-repeat: no-repeat !important;
  background-attachment: scroll !important; /* Changed from fixed for mobile compatibility */

  /* Modern viewport units with fallbacks */
  min-height: 100vh !important;
  min-height: 100dvh !important; /* Dynamic viewport height */
  min-height: 100svh !important; /* Small viewport height */

  width: 100% !important;
  width: 100vw !important;
  width: 100dvw !important; /* Dynamic viewport width */

  overflow-x: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
  position: relative !important;
}

body {
  background-color: #364035 !important;
  background: linear-gradient(to bottom right, #364035, #8B9A8C, #364035) !important;
  background-size: 100% 100% !important;
  background-repeat: no-repeat !important;
  background-attachment: scroll !important; /* Changed from fixed for mobile compatibility */

  /* Modern viewport units with fallbacks */
  min-height: 100vh !important;
  min-height: 100dvh !important; /* Dynamic viewport height */
  min-height: 100svh !important; /* Small viewport height */

  width: 100% !important;
  width: 100vw !important;
  width: 100dvw !important; /* Dynamic viewport width */

  overflow-x: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
  position: relative !important;
}

/* Root container background coverage */
#__next, [data-nextjs-scroll-focus-boundary] {
  background: linear-gradient(to bottom right, #364035, #8B9A8C, #364035) !important;
  background-size: 100% 100% !important;
  background-repeat: no-repeat !important;
  min-height: 100vh !important;
  min-height: 100dvh !important;
  min-height: 100svh !important;
  width: 100% !important;
}

/* Specific fixes for mobile landscape orientation */
@media screen and (orientation: landscape) {
  html, body {
    background-color: #364035 !important;
    background: linear-gradient(to bottom right, #364035, #8B9A8C, #364035) !important;
    background-size: 100% 100% !important;
    background-repeat: no-repeat !important;
    background-attachment: scroll !important;

    /* Use large viewport height for landscape */
    min-height: 100vh !important;
    min-height: 100lvh !important; /* Large viewport height for landscape */
    min-height: 100dvh !important; /* Dynamic viewport height */

    height: 100vh !important;
    height: 100lvh !important;
    height: 100dvh !important;

    width: 100vw !important;
    width: 100dvw !important;
    overflow-x: hidden !important;
    overflow-y: auto !important;
  }

  /* Ensure all containers maintain background in landscape */
  .mobile-full-height {
    min-height: 100vh !important;
    min-height: 100lvh !important;
    min-height: 100dvh !important;
    background: linear-gradient(to bottom right, #364035, #8B9A8C, #364035) !important;
    background-size: 100% 100% !important;
    background-repeat: no-repeat !important;
  }

  .min-h-screen {
    min-height: 100vh !important;
    min-height: 100lvh !important;
    min-height: 100dvh !important;
  }

  /* Ensure Next.js containers maintain background */
  #__next, [data-nextjs-scroll-focus-boundary], main {
    background: linear-gradient(to bottom right, #364035, #8B9A8C, #364035) !important;
    background-size: 100% 100% !important;
    background-repeat: no-repeat !important;
    min-height: 100lvh !important;
    min-height: 100dvh !important;
  }
}

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --background: transparent;
    --foreground: 0 0% 3.9%;
    --card: transparent;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 111 10% 23%;
    --primary-foreground: 0 0% 98%;
    --secondary: 125 6% 57%;
    --secondary-foreground: 0 0% 98%;
    --muted: 48 100% 96.1%; /* Cream color */
    --muted-foreground: 48 100% 85%; /* Cream foreground */
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 111 10% 23%;
    --primary-foreground: 0 0% 98%;
    --secondary: 125 6% 57%;
    --secondary-foreground: 0 0% 9%;
    --muted: 48 100% 14.9%; /* Dark cream color */
    --muted-foreground: 48 100% 75%; /* Dark cream foreground */
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply text-foreground;
  }
}

@keyframes beam {
  0% { transform: translateY(-100%); }
  100% { transform: translateY(100%); }
}

/* Mobile-specific styles for iPhone notch and safe areas */
@supports (padding: max(0px)) {
  html {
    padding-top: max(var(--safe-area-inset-top), 0px);
    padding-left: max(var(--safe-area-inset-left), 0px);
    padding-right: max(var(--safe-area-inset-right), 0px);
    padding-bottom: max(var(--safe-area-inset-bottom), 0px);
  }

  /* Fix for mobile browsers address bar */
  .mobile-full-height {
    min-height: 100vh !important;
    min-height: 100dvh !important;
    min-height: 100svh !important;
    background: linear-gradient(to bottom right, #364035, #8B9A8C, #364035) !important;
    background-size: 100% 100% !important;
    background-repeat: no-repeat !important;
  }
}

/* Safari-specific fixes for iPhone */
@supports (-webkit-touch-callout: none) {
  html {
    height: 100dvh;
    height: -webkit-fill-available;
    background-attachment: scroll;
    -webkit-overflow-scrolling: touch;
  }

  body {
    height: 100dvh;
    height: -webkit-fill-available;
    background-attachment: scroll;
    -webkit-overflow-scrolling: touch;
  }

  /* Create a comprehensive background overlay for Safari */
  html::before {
    content: '';
    position: fixed;
    top: -50px;
    left: -50px;
    right: -50px;
    bottom: -50px;
    background: linear-gradient(to bottom right, #364035, #8B9A8C, #364035);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    z-index: -100;
    pointer-events: none;
  }

  /* Ensure footer content has proper bottom padding */
  footer {
    padding-bottom: max(env(safe-area-inset-bottom), 20px) !important;
  }
}

/* Additional mobile optimizations */
@media screen and (max-width: 768px) {
  /* Prevent horizontal scroll */
  body {
    overflow-x: hidden;
  }

  /* Improve touch targets */
  button, a {
    min-height: 44px;
    min-width: 44px;
  }

  /* Optimize text for mobile */
  body {
    -webkit-text-size-adjust: 100%;
    text-size-adjust: 100%;
  }
}

/* Portrait orientation specific fixes */
@media screen and (orientation: portrait) {
  html, body {
    background: linear-gradient(to bottom right, #364035, #8B9A8C, #364035) !important;
    background-size: 100% 100% !important;
    background-repeat: no-repeat !important;
    background-attachment: scroll !important;
    min-height: 100vh !important;
    min-height: 100dvh !important;
    min-height: 100svh !important;
  }
}

/* Prevent white backgrounds on any elements except root containers */
*:not(html):not(body):not(#__next):not([data-nextjs-scroll-focus-boundary]):not(main) {
  background-color: transparent;
}

/* Ensure root elements maintain background */
html, body, #__next, [data-nextjs-scroll-focus-boundary], main {
  background: linear-gradient(to bottom right, #364035, #8B9A8C, #364035) !important;
  background-size: 100% 100% !important;
  background-repeat: no-repeat !important;
  background-attachment: scroll !important;
}

/* Accent Color Implementation for Form Controls */
/* Apply accent color to form controls */
input[type="checkbox"],
input[type="radio"],
input[type="range"],
progress {
  accent-color: var(--accent-primary);
}

/* Enhanced form control styling with accent colors */
input[type="text"],
input[type="email"],
input[type="tel"],
input[type="password"],
input[type="url"],
textarea,
select {
  accent-color: var(--accent-primary);
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="tel"]:focus,
input[type="password"]:focus,
input[type="url"]:focus,
textarea:focus,
select:focus {
  outline: 2px solid var(--accent-primary);
  outline-offset: 2px;
  border-color: var(--accent-primary);
}

/* Button accent styling */
button:focus-visible {
  outline: 2px solid var(--accent-primary);
  outline-offset: 2px;
}

/* Link accent styling */
a:focus-visible {
  outline: 2px solid var(--accent-primary);
  outline-offset: 2px;
}

/* Accent color for interactive elements */
.accent-bg {
  background-color: var(--accent-primary);
}

.accent-text {
  color: var(--accent-primary);
}

.accent-border {
  border-color: var(--accent-primary);
}

.accent-hover:hover {
  background-color: var(--accent-hover);
}

/* Selection styling with accent color */
::selection {
  background-color: var(--accent-medium);
  color: white;
}

::-moz-selection {
  background-color: var(--accent-medium);
  color: white;
}
