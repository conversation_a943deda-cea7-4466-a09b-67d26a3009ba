import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { BackgroundBeams } from "@/components/ui/background-beams";
import { GlowingCardContainer } from "@/components/ui/glowing-card";
import { NewShinyButton } from "@/components/ui/new-shiny-button";
import { Users, Target, Lightbulb, Heart } from "lucide-react";

export default function About() {
  return (
    <div className="min-h-screen relative overflow-hidden">
      <BackgroundBeams />

      {/* Hero Section */}
      <section className="relative z-10 w-full px-4 py-20 pt-32">
        <div className="text-center max-w-6xl mx-auto">
          <h1 className="text-4xl md:text-6xl font-black mb-6 leading-none text-white drop-shadow-lg">
            We're on a mission to empower entrepreneurs
          </h1>
          <p className="text-xl md:text-2xl text-white/90 mb-8 leading-relaxed max-w-4xl mx-auto drop-shadow-sm">
            Building the future of business operations, one entrepreneur at a time.
          </p>
        </div>
      </section>

      {/* Our Mission Section */}
      <section className="relative z-10 py-20 border-t border-white/20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center">
              <GlowingCardContainer>
                <Card className="bg-white/15 backdrop-blur-md border border-white/25 shadow-xl rounded-2xl p-8">
                  <div className="flex items-center justify-center space-x-2 mb-6">
                    <Target className="h-8 w-8 text-white drop-shadow-lg" />
                    <h2 className="text-3xl font-bold text-white drop-shadow-lg">Our Mission</h2>
                  </div>
                  <div className="flex items-center justify-center mb-6">
                    <div className="w-20 h-20 rounded-full flex items-center justify-center" style={{backgroundColor: 'rgba(244, 241, 232, 0.2)', border: '2px solid #F4F1E8'}}>
                      <Heart className="w-10 h-10 text-white drop-shadow-lg" />
                    </div>
                  </div>
                  <p className="text-lg text-white/90 leading-relaxed drop-shadow-sm mb-4 text-center">
                    We started Vierla because we saw too many small business owners and freelancers struggling to manage a dozen different software subscriptions. It's costly, complex, and time-consuming. Our mission is to consolidate all the essential tools into a single, intelligent, and affordable platform, giving entrepreneurs their time back so they can focus on what they do best: growing their business.
                  </p>
                  <p className="text-lg text-white/90 leading-relaxed drop-shadow-sm text-center">
                    Every feature we build is designed with the modern entrepreneur in mind - from the solo freelancer just starting out to the growing agency scaling their operations. We believe that powerful business tools shouldn't require a technical degree or a massive budget to use effectively.
                  </p>
                </Card>
              </GlowingCardContainer>
            </div>
          </div>
        </div>
      </section>

      {/* Our Vision Section */}
      <section className="container space-y-6 py-8 md:py-12 lg:py-24 border-t border-white/20 mx-auto">
        <div className="mx-auto max-w-4xl">
          <div className="text-center">
            <GlowingCardContainer>
              <Card className="bg-white/15 backdrop-blur-md border border-white/25 shadow-xl rounded-2xl p-8">
                <div className="flex items-center justify-center space-x-2 mb-6">
                  <Lightbulb className="h-8 w-8 text-white drop-shadow-lg" />
                  <h2 className="text-3xl font-bold text-white drop-shadow-lg">Our Vision</h2>
                </div>
                <div className="flex items-center justify-center mb-6">
                  <div className="w-20 h-20 rounded-full flex items-center justify-center" style={{backgroundColor: 'rgba(244, 241, 232, 0.2)', border: '2px solid #F4F1E8'}}>
                    <Lightbulb className="w-10 h-10 text-white drop-shadow-lg" />
                  </div>
                </div>
                <p className="text-lg text-white/90 leading-relaxed drop-shadow-sm mb-4 text-center">
                  We envision a future where starting and running a business is radically simpler. By leveraging the power of AI, we aim to automate administrative busywork and provide powerful insights that were once only available to large corporations. We're building the operating system for the next generation of business.
                </p>
                <p className="text-lg text-white/90 leading-relaxed drop-shadow-sm text-center">
                  Our vision extends beyond just software - we're creating an ecosystem where entrepreneurs can thrive, connect, and grow together. We believe that when we remove the barriers to business success, we unlock human potential and drive innovation across every industry.
                </p>
              </Card>
            </GlowingCardContainer>
          </div>
        </div>
      </section>

      {/* Meet the Team Section */}
      <section className="container space-y-6 py-8 md:py-12 lg:py-24 border-t border-white/20 mx-auto">
        <div className="mx-auto max-w-6xl">
          <div className="text-center mb-12">
            <div className="flex items-center justify-center space-x-2 mb-4">
              <Users className="h-8 w-8 text-white drop-shadow-lg" />
              <h2 className="text-3xl font-bold text-white drop-shadow-lg">Meet the Team</h2>
            </div>
            <div className="w-full h-px bg-gradient-to-r from-transparent via-white/20 to-transparent mb-6"></div>
            <p className="text-lg text-white/90 max-w-2xl mx-auto drop-shadow-sm">
              We're a passionate group of entrepreneurs, developers, and designers united by our mission to simplify business operations.
            </p>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 justify-center">
            {[1, 2, 3, 4, 5, 6].map((member) => (
              <GlowingCardContainer key={member}>
                <Card className="text-center bg-white/15 backdrop-blur-md border border-white/25 shadow-xl rounded-2xl">
                  <CardHeader className="text-center">
                    <div className="w-24 h-24 mx-auto rounded-full flex items-center justify-center mb-4" style={{backgroundColor: 'rgba(244, 241, 232, 0.2)', border: '2px solid #F4F1E8'}}>
                      <Users className="h-12 w-12 text-white drop-shadow-lg" />
                    </div>
                    <CardTitle className="text-white drop-shadow-lg text-center">Team Member {member}</CardTitle>
                    <CardDescription className="text-white/80 drop-shadow-sm text-center">Position Title</CardDescription>
                  </CardHeader>
                  <CardContent className="text-center">
                    <p className="text-sm text-white/70 drop-shadow-sm text-center">
                      Brief bio and background information will be added here.
                    </p>
                  </CardContent>
                </Card>
              </GlowingCardContainer>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="container space-y-6 py-8 md:py-12 lg:py-24 border-t border-white/20 mx-auto">
        <div className="mx-auto flex max-w-[58rem] flex-col items-center space-y-4 text-center">
          <div className="w-full h-px bg-gradient-to-r from-transparent via-white/20 to-transparent mb-6"></div>
          <h2 className="font-heading text-3xl leading-[1.1] sm:text-3xl md:text-6xl text-white drop-shadow-lg text-center">
            Ready to join our mission?
          </h2>
          <p className="max-w-[42rem] leading-normal text-white/90 sm:text-xl sm:leading-8 drop-shadow-sm text-center">
            Be part of the future of business operations. Start your journey with Vierla today.
          </p>
          <div className="flex justify-center">
            <NewShinyButton className="px-8 py-4 text-lg">
              <a href="/apply">Get Started</a>
            </NewShinyButton>
          </div>
          <div className="w-full h-px bg-gradient-to-r from-transparent via-white/20 to-transparent mt-6"></div>
        </div>
      </section>

    </div>
  );
}
