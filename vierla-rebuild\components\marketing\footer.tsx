import React from "react";
import Link from "next/link";

export function Footer() {
  return (
    <footer className="bg-background border-t py-12">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          <div>
            <h2 className="text-xl font-bold mb-4 text-white drop-shadow-lg">Vierla</h2>
            <p className="text-white/80 text-sm drop-shadow-sm">
              The AI-Powered Platform for Modern Business.
            </p>
          </div>
          <div>
            <h3 className="font-semibold mb-4 text-white drop-shadow-lg">Product</h3>
            <ul className="space-y-2 text-sm">
              <li><Link href="/features" className="text-white/70 hover:text-white drop-shadow-sm">Features</Link></li>
              <li><Link href="/pricing" className="text-white/70 hover:text-white drop-shadow-sm">Pricing</Link></li>
              <li><Link href="/provider-app" className="text-white/70 hover:text-white drop-shadow-sm">For Providers</Link></li>
              <li><Link href="/apply" className="text-white/70 hover:text-white drop-shadow-sm">Apply</Link></li>
            </ul>
          </div>
          <div>
            <h3 className="font-semibold mb-4 text-white drop-shadow-lg">Company</h3>
            <ul className="space-y-2 text-sm">
              <li><Link href="/about" className="text-white/70 hover:text-white drop-shadow-sm">About Us</Link></li>
              <li><Link href="/contact" className="text-white/70 hover:text-white drop-shadow-sm">Contact Us</Link></li>
            </ul>
          </div>
          <div>
            <h3 className="font-semibold mb-4 text-white drop-shadow-lg">Legal</h3>
            <ul className="space-y-2 text-sm">
              <li><Link href="/privacy" className="text-white/70 hover:text-white drop-shadow-sm">Privacy Policy</Link></li>
              <li><Link href="/terms" className="text-white/70 hover:text-white drop-shadow-sm">Terms of Service</Link></li>
            </ul>
          </div>
        </div>
        <div className="mt-10 border-t border-border pt-8 text-center text-white/60 text-xs drop-shadow-sm">
          <p>© {new Date().getFullYear()} Vierla, Inc. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
}
