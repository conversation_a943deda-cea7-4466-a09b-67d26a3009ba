{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/background-beams.tsx"], "sourcesContent": ["\"use client\";\nimport React from \"react\";\nimport { motion } from \"framer-motion\";\nimport { cn } from \"@/lib/utils\";\n\nexport const BackgroundBeams = ({ className }: { className?: string }) => {\n  return (\n    <div\n      className={cn(\n        \"absolute top-0 left-0 w-full h-full -z-10\",\n        className\n      )}\n    >\n      <div className=\"relative w-full h-full overflow-hidden\">\n        <div className=\"absolute inset-0 bg-zinc-900\"></div>\n        <div className=\"absolute h-full w-full\">\n          {/* Placeholder for beam elements */}\n          {/* This component often requires more complex SVG/div structures for the beams themselves. */}\n          {/* The following is a simplified representation. Refer to the source for the full SVG implementation. */}\n          <div className=\"absolute top-0 left-1/4 h-full w-px bg-gradient-to-b from-transparent via-purple-500 to-transparent animate-[beam_8s_linear_infinite]\"></div>\n          <div className=\"absolute top-0 left-1/2 h-full w-px bg-gradient-to-b from-transparent via-purple-500 to-transparent animate-[beam_10s_linear_infinite_2s_]\"></div>\n          <div className=\"absolute top-0 left-3/4 h-full w-px bg-gradient-to-b from-transparent via-purple-500 to-transparent animate-[beam_9s_linear_infinite_1s_]\"></div>\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKO,MAAM,kBAAkB;QAAC,EAAE,SAAS,EAA0B;IACnE,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,6CACA;kBAGF,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAI,WAAU;;sCAIb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKzB;KArBa", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/glowing-card.tsx"], "sourcesContent": ["\"use client\";\nimport React from \"react\";\nimport { cn } from \"@/lib/utils\";\n\nexport const GlowingCardContainer = ({\n  children,\n  className,\n}: {\n  children: React.ReactNode;\n  className?: string;\n}) => {\n  return (\n    <div className={cn(\"relative w-full h-full group p-[1px]\", className)}>\n      <div\n        className=\"absolute inset-0 rounded-2xl bg-gradient-to-r from-[#B8956A]/30 via-[#B8956A]/60 to-[#B8956A]/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\n      />\n      <div className=\"relative w-full h-full bg-card/50 backdrop-blur-md border border-border/25 shadow-xl rounded-[calc(1rem-1px)] overflow-hidden\">\n        {children}\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIO,MAAM,uBAAuB;QAAC,EACnC,QAAQ,EACR,SAAS,EAIV;IACC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;;0BACzD,6LAAC;gBACC,WAAU;;;;;;0BAEZ,6LAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAIT;KAjBa", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/marquee-effect.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useRef } from \"react\";\nimport {\n  motion,\n  useScroll,\n  useSpring,\n  useTransform,\n  useMotionValue,\n  useVelocity,\n  useAnimationFrame,\n} from \"framer-motion\";\nimport { wrap } from \"@motionone/utils\";\nimport { cn } from \"@/lib/utils\";\n\ntype MarqueeAnimationProps = {\n  children: string;\n  className?: string;\n  direction?: \"left\" | \"right\";\n  baseVelocity: number;\n};\n\nfunction MarqueeAnimation({\n  children,\n  className,\n  direction = \"left\",\n  baseVelocity = 10,\n}: MarqueeAnimationProps) {\n  const baseX = useMotionValue(0);\n  const { scrollY } = useScroll();\n  const scrollVelocity = useVelocity(scrollY);\n  const smoothVelocity = useSpring(scrollVelocity, {\n    damping: 50,\n    stiffness: 400,\n  });\n  const velocityFactor = useTransform(smoothVelocity, [0, 1000], [0, 0], {\n    clamp: false,\n  });\n\n  const x = useTransform(baseX, (v) => `${wrap(-20, -45, v)}%`);\n\n  const directionFactor = useRef<number>(1);\n  useAnimationFrame((t, delta) => {\n    let moveBy = directionFactor.current * baseVelocity * (delta / 1000);\n\n    if (direction == \"left\") {\n      directionFactor.current = 1;\n    } else if (direction == \"right\") {\n      directionFactor.current = -1;\n    }\n\n    moveBy += directionFactor.current * moveBy * velocityFactor.get();\n\n    baseX.set(baseX.get() + moveBy);\n  });\n\n  return (\n    <div className=\"overflow-hidden max-w-[100vw] text-nowrap flex-nowrap flex relative\">\n      <motion.div\n        className={cn(\n          \"font-bold uppercase text-5xl flex flex-nowrap text-nowrap *:block *:me-10 text-white drop-shadow-lg\",\n          className\n        )}\n        style={{ x }}\n      >\n        <span>{children}</span>\n        <span>{children}</span>\n        <span>{children}</span>\n        <span>{children}</span>\n      </motion.div>\n    </div>\n  );\n}\n\nexport { MarqueeAnimation };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;;;AAbA;;;;;AAsBA,SAAS,iBAAiB,KAKF;QALE,EACxB,QAAQ,EACR,SAAS,EACT,YAAY,MAAM,EAClB,eAAe,EAAE,EACK,GALE;;IAMxB,MAAM,QAAQ,CAAA,GAAA,qLAAA,CAAA,iBAAc,AAAD,EAAE;IAC7B,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD;IAC5B,MAAM,iBAAiB,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;IACnC,MAAM,iBAAiB,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB;QAC/C,SAAS;QACT,WAAW;IACb;IACA,MAAM,iBAAiB,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB;QAAC;QAAG;KAAK,EAAE;QAAC;QAAG;KAAE,EAAE;QACrE,OAAO;IACT;IAEA,MAAM,IAAI,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE;4CAAO,CAAC,IAAM,AAAC,GAAoB,OAAlB,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,CAAC,IAAI,CAAC,IAAI,IAAG;;IAE1D,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAU;IACvC,CAAA,GAAA,wLAAA,CAAA,oBAAiB,AAAD;8CAAE,CAAC,GAAG;YACpB,IAAI,SAAS,gBAAgB,OAAO,GAAG,eAAe,CAAC,QAAQ,IAAI;YAEnE,IAAI,aAAa,QAAQ;gBACvB,gBAAgB,OAAO,GAAG;YAC5B,OAAO,IAAI,aAAa,SAAS;gBAC/B,gBAAgB,OAAO,GAAG,CAAC;YAC7B;YAEA,UAAU,gBAAgB,OAAO,GAAG,SAAS,eAAe,GAAG;YAE/D,MAAM,GAAG,CAAC,MAAM,GAAG,KAAK;QAC1B;;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,uGACA;YAEF,OAAO;gBAAE;YAAE;;8BAEX,6LAAC;8BAAM;;;;;;8BACP,6LAAC;8BAAM;;;;;;8BACP,6LAAC;8BAAM;;;;;;8BACP,6LAAC;8BAAM;;;;;;;;;;;;;;;;;AAIf;GAlDS;;QAMO,qLAAA,CAAA,iBAAc;QACR,4KAAA,CAAA,YAAS;QACN,8KAAA,CAAA,cAAW;QACX,4KAAA,CAAA,YAAS;QAIT,+KAAA,CAAA,eAAY;QAIzB,+KAAA,CAAA,eAAY;QAGtB,wLAAA,CAAA,oBAAiB;;;KApBV", "debugId": null}}]}