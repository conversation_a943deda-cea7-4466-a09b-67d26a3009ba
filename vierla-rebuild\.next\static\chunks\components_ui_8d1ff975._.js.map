{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/background-beams.tsx"], "sourcesContent": ["\"use client\";\nimport React from \"react\";\nimport { motion } from \"framer-motion\";\nimport { cn } from \"@/lib/utils\";\n\nexport const BackgroundBeams = ({ className }: { className?: string }) => {\n  return (\n    <div\n      className={cn(\n        \"absolute top-0 left-0 w-full h-full -z-10\",\n        className\n      )}\n    >\n      <div className=\"relative w-full h-full overflow-hidden\">\n        <div className=\"absolute inset-0 bg-zinc-900\"></div>\n        <div className=\"absolute h-full w-full\">\n          {/* Placeholder for beam elements */}\n          {/* This component often requires more complex SVG/div structures for the beams themselves. */}\n          {/* The following is a simplified representation. Refer to the source for the full SVG implementation. */}\n          <div className=\"absolute top-0 left-1/4 h-full w-px bg-gradient-to-b from-transparent via-purple-500 to-transparent animate-[beam_8s_linear_infinite]\"></div>\n          <div className=\"absolute top-0 left-1/2 h-full w-px bg-gradient-to-b from-transparent via-purple-500 to-transparent animate-[beam_10s_linear_infinite_2s_]\"></div>\n          <div className=\"absolute top-0 left-3/4 h-full w-px bg-gradient-to-b from-transparent via-purple-500 to-transparent animate-[beam_9s_linear_infinite_1s_]\"></div>\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKO,MAAM,kBAAkB;QAAC,EAAE,SAAS,EAA0B;IACnE,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,6CACA;kBAGF,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAI,WAAU;;sCAIb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKzB;KArBa", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/glowing-card.tsx"], "sourcesContent": ["\"use client\";\nimport React from \"react\";\nimport { cn } from \"@/lib/utils\";\n\nexport const GlowingCardContainer = ({\n  children,\n  className,\n}: {\n  children: React.ReactNode;\n  className?: string;\n}) => {\n  return (\n    <div className={cn(\"relative w-full h-full group p-[1px]\", className)}>\n      <div\n        className=\"absolute inset-0 rounded-2xl bg-gradient-to-r from-[#B8956A]/30 via-[#B8956A]/60 to-[#B8956A]/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500\"\n      />\n      <div className=\"relative w-full h-full bg-card/50 backdrop-blur-md border border-border/25 shadow-xl rounded-[calc(1rem-1px)] overflow-hidden\">\n        {children}\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIO,MAAM,uBAAuB;QAAC,EACnC,QAAQ,EACR,SAAS,EAIV;IACC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;;0BACzD,6LAAC;gBACC,WAAU;;;;;;0BAEZ,6LAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAIT;KAjBa", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/new-shiny-button.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { motion, type AnimationProps } from \"framer-motion\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst animationProps: AnimationProps = {\n  initial: { \"--x\": \"100%\", scale: 0.8 },\n  animate: { \"--x\": \"-100%\", scale: 1 },\n  whileTap: { scale: 0.95 },\n  transition: {\n    repeat: Infinity,\n    repeatType: \"loop\",\n    repeatDelay: 1,\n    type: \"spring\",\n    stiffness: 20,\n    damping: 15,\n    mass: 2,\n    scale: {\n      type: \"spring\",\n      stiffness: 200,\n      damping: 5,\n      mass: 0.5,\n    },\n  },\n};\n\ninterface NewShinyButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const NewShinyButton: React.FC<NewShinyButtonProps> = ({\n  children,\n  className,\n  ...props\n}) => {\n  return (\n    <motion.button\n      {...animationProps}\n      {...props}\n      className={cn(\n        \"relative rounded-lg px-6 py-2 font-medium backdrop-blur-xl transition-shadow duration-300 ease-in-out hover:shadow\",\n        \"bg-[#B8956A] text-white drop-shadow-lg\",\n        \"dark:bg-[#B8956A] dark:hover:shadow-[0_0_20px_rgba(184,149,106,0.3)]\",\n        // Remove whitespace-nowrap to allow text wrapping for longer content\n        \"flex items-center justify-center text-center\",\n        className\n      )}\n      style={{\n        \"--primary\": \"184 149 106\", // #B8956A in RGB\n      } as React.CSSProperties}\n    >\n      <span\n        className=\"relative block size-full text-sm uppercase tracking-wide text-white font-medium\"\n        style={{\n          maskImage:\n            \"linear-gradient(-75deg,rgb(184,149,106) calc(var(--x) + 20%),transparent calc(var(--x) + 30%),rgb(184,149,106) calc(var(--x) + 100%))\",\n        }}\n      >\n        {children}\n      </span>\n      <span\n        style={{\n          mask: \"linear-gradient(rgb(0,0,0), rgb(0,0,0)) content-box,linear-gradient(rgb(0,0,0), rgb(0,0,0))\",\n          maskComposite: \"exclude\",\n        }}\n        className=\"absolute inset-0 z-10 block rounded-[inherit] bg-[linear-gradient(-75deg,rgba(184,149,106,0.1)_calc(var(--x)+20%),rgba(184,149,106,0.5)_calc(var(--x)+25%),rgba(184,149,106,0.1)_calc(var(--x)+100%))] p-px\"\n      ></span>\n    </motion.button>\n  );\n};\n\nexport default NewShinyButton;\n"], "names": [], "mappings": ";;;;;AAGA;AAEA;AALA;;;;AAOA,MAAM,iBAAiC;IACrC,SAAS;QAAE,OAAO;QAAQ,OAAO;IAAI;IACrC,SAAS;QAAE,OAAO;QAAS,OAAO;IAAE;IACpC,UAAU;QAAE,OAAO;IAAK;IACxB,YAAY;QACV,QAAQ;QACR,YAAY;QACZ,aAAa;QACb,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;QACN,OAAO;YACL,MAAM;YACN,WAAW;YACX,SAAS;YACT,MAAM;QACR;IACF;AACF;AAQO,MAAM,iBAAgD;QAAC,EAC5D,QAAQ,EACR,SAAS,EACT,GAAG,OACJ;IACC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACX,GAAG,cAAc;QACjB,GAAG,KAAK;QACT,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sHACA,0CACA,wEACA,qEAAqE;QACrE,gDACA;QAEF,OAAO;YACL,aAAa;QACf;;0BAEA,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,WACE;gBACJ;0BAEC;;;;;;0BAEH,6LAAC;gBACC,OAAO;oBACL,MAAM;oBACN,eAAe;gBACjB;gBACA,WAAU;;;;;;;;;;;;AAIlB;KAvCa;uCAyCE", "debugId": null}}]}