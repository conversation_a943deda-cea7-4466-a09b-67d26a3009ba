{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/background-beams.tsx"], "sourcesContent": ["\"use client\";\nimport React from \"react\";\nimport { motion } from \"framer-motion\";\nimport { cn } from \"@/lib/utils\";\n\nexport const BackgroundBeams = ({ className }: { className?: string }) => {\n  return (\n    <div\n      className={cn(\n        \"absolute top-0 left-0 w-full h-full -z-10\",\n        className\n      )}\n    >\n      <div className=\"relative w-full h-full overflow-hidden\">\n        <div className=\"absolute inset-0 bg-zinc-900\"></div>\n        <div className=\"absolute h-full w-full\">\n          {/* Placeholder for beam elements */}\n          {/* This component often requires more complex SVG/div structures for the beams themselves. */}\n          {/* The following is a simplified representation. Refer to the source for the full SVG implementation. */}\n          <div className=\"absolute top-0 left-1/4 h-full w-px bg-gradient-to-b from-transparent via-purple-500 to-transparent animate-[beam_8s_linear_infinite]\"></div>\n          <div className=\"absolute top-0 left-1/2 h-full w-px bg-gradient-to-b from-transparent via-purple-500 to-transparent animate-[beam_10s_linear_infinite_2s_]\"></div>\n          <div className=\"absolute top-0 left-3/4 h-full w-px bg-gradient-to-b from-transparent via-purple-500 to-transparent animate-[beam_9s_linear_infinite_1s_]\"></div>\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKO,MAAM,kBAAkB,CAAC,EAAE,SAAS,EAA0B;IACnE,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6CACA;kBAGF,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAI,WAAU;;sCAIb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKzB", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/golden-glow-effect.tsx"], "sourcesContent": ["\"use client\";\n\nimport { memo, useCallback, useEffect, useRef } from \"react\";\nimport { cn } from \"@/lib/utils\";\nimport { animate } from \"motion/react\";\n\ninterface GoldenGlowEffectProps {\n  blur?: number;\n  inactiveZone?: number;\n  proximity?: number;\n  spread?: number;\n  glow?: boolean;\n  className?: string;\n  disabled?: boolean;\n  movementDuration?: number;\n  borderWidth?: number;\n}\n\nconst GoldenGlowEffect = memo(\n  ({\n    blur = 0,\n    inactiveZone = 0.7,\n    proximity = 0,\n    spread = 20,\n    glow = false,\n    className,\n    movementDuration = 2,\n    borderWidth = 1,\n    disabled = true,\n  }: GoldenGlowEffectProps) => {\n    const containerRef = useRef<HTMLDivElement>(null);\n    const lastPosition = useRef({ x: 0, y: 0 });\n    const animationFrameRef = useRef<number>(0);\n\n    const handleMove = useCallback(\n      (e?: MouseEvent | { x: number; y: number }) => {\n        if (!containerRef.current) return;\n\n        if (animationFrameRef.current) {\n          cancelAnimationFrame(animationFrameRef.current);\n        }\n\n        animationFrameRef.current = requestAnimationFrame(() => {\n          const element = containerRef.current;\n          if (!element) return;\n\n          const { left, top, width, height } = element.getBoundingClientRect();\n          const mouseX = e?.x ?? lastPosition.current.x;\n          const mouseY = e?.y ?? lastPosition.current.y;\n\n          if (e) {\n            lastPosition.current = { x: mouseX, y: mouseY };\n          }\n\n          const center = [left + width * 0.5, top + height * 0.5];\n          const distanceFromCenter = Math.hypot(\n            mouseX - center[0],\n            mouseY - center[1]\n          );\n          const inactiveRadius = 0.5 * Math.min(width, height) * inactiveZone;\n\n          if (distanceFromCenter < inactiveRadius) {\n            element.style.setProperty(\"--active\", \"0\");\n            return;\n          }\n\n          const isActive =\n            mouseX > left - proximity &&\n            mouseX < left + width + proximity &&\n            mouseY > top - proximity &&\n            mouseY < top + height + proximity;\n\n          element.style.setProperty(\"--active\", isActive ? \"1\" : \"0\");\n\n          if (!isActive) return;\n\n          const currentAngle =\n            parseFloat(element.style.getPropertyValue(\"--start\")) || 0;\n          let targetAngle =\n            (180 * Math.atan2(mouseY - center[1], mouseX - center[0])) /\n              Math.PI +\n            90;\n\n          const angleDiff = ((targetAngle - currentAngle + 180) % 360) - 180;\n          const newAngle = currentAngle + angleDiff;\n\n          animate(currentAngle, newAngle, {\n            duration: movementDuration,\n            ease: [0.16, 1, 0.3, 1],\n            onUpdate: (value) => {\n              element.style.setProperty(\"--start\", String(value));\n            },\n          });\n        });\n      },\n      [inactiveZone, proximity, movementDuration]\n    );\n\n    useEffect(() => {\n      if (disabled) return;\n\n      const handleScroll = () => handleMove();\n      const handlePointerMove = (e: PointerEvent) => handleMove(e);\n\n      window.addEventListener(\"scroll\", handleScroll, { passive: true });\n      document.body.addEventListener(\"pointermove\", handlePointerMove, {\n        passive: true,\n      });\n\n      return () => {\n        if (animationFrameRef.current) {\n          cancelAnimationFrame(animationFrameRef.current);\n        }\n        window.removeEventListener(\"scroll\", handleScroll);\n        document.body.removeEventListener(\"pointermove\", handlePointerMove);\n      };\n    }, [handleMove, disabled]);\n\n    return (\n      <>\n        <div\n          className={cn(\n            \"pointer-events-none absolute -inset-px hidden rounded-[inherit] border opacity-0 transition-opacity\",\n            glow && \"opacity-100\",\n            \"border-[#B8956A]\",\n            disabled && \"!block\"\n          )}\n        />\n        <div\n          ref={containerRef}\n          style={\n            {\n              \"--blur\": `${blur}px`,\n              \"--spread\": spread,\n              \"--start\": \"0\",\n              \"--active\": \"0\",\n              \"--glowingeffect-border-width\": `${borderWidth}px`,\n              \"--repeating-conic-gradient-times\": \"5\",\n              \"--gradient\": `radial-gradient(circle, #B8956A 10%, #B8956A00 20%),\n                radial-gradient(circle at 40% 40%, #D4AF37 5%, #D4AF3700 15%),\n                radial-gradient(circle at 60% 60%, #B8956A 10%, #B8956A00 20%), \n                radial-gradient(circle at 40% 60%, #DAA520 10%, #DAA52000 20%),\n                repeating-conic-gradient(\n                  from 236.84deg at 50% 50%,\n                  #B8956A 0%,\n                  #D4AF37 calc(25% / var(--repeating-conic-gradient-times)),\n                  #B8956A calc(50% / var(--repeating-conic-gradient-times)), \n                  #DAA520 calc(75% / var(--repeating-conic-gradient-times)),\n                  #B8956A calc(100% / var(--repeating-conic-gradient-times))\n                )`,\n            } as React.CSSProperties\n          }\n          className={cn(\n            \"pointer-events-none absolute inset-0 rounded-[inherit] opacity-100 transition-opacity\",\n            glow && \"opacity-100\",\n            blur > 0 && \"blur-[var(--blur)] \",\n            className,\n            disabled && \"!hidden\"\n          )}\n        >\n          <div\n            className={cn(\n              \"glow\",\n              \"rounded-[inherit]\",\n              'after:content-[\"\"] after:rounded-[inherit] after:absolute after:inset-[calc(-1*var(--glowingeffect-border-width))]',\n              \"after:[border:var(--glowingeffect-border-width)_solid_transparent]\",\n              \"after:[background:var(--gradient)] after:[background-attachment:fixed]\",\n              \"after:opacity-[var(--active)] after:transition-opacity after:duration-300\",\n              \"after:[mask-clip:padding-box,border-box]\",\n              \"after:[mask-composite:intersect]\",\n              \"after:[mask-image:linear-gradient(#0000,#0000),conic-gradient(from_calc((var(--start)-var(--spread))*1deg),#00000000_0deg,#fff,#00000000_calc(var(--spread)*2deg))]\"\n            )}\n          />\n        </div>\n      </>\n    );\n  }\n);\n\nGoldenGlowEffect.displayName = \"GoldenGlowEffect\";\n\nexport { GoldenGlowEffect };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAkBA,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EAC1B,CAAC,EACC,OAAO,CAAC,EACR,eAAe,GAAG,EAClB,YAAY,CAAC,EACb,SAAS,EAAE,EACX,OAAO,KAAK,EACZ,SAAS,EACT,mBAAmB,CAAC,EACpB,cAAc,CAAC,EACf,WAAW,IAAI,EACO;IACtB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IACzC,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAU;IAEzC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC3B,CAAC;QACC,IAAI,CAAC,aAAa,OAAO,EAAE;QAE3B,IAAI,kBAAkB,OAAO,EAAE;YAC7B,qBAAqB,kBAAkB,OAAO;QAChD;QAEA,kBAAkB,OAAO,GAAG,sBAAsB;YAChD,MAAM,UAAU,aAAa,OAAO;YACpC,IAAI,CAAC,SAAS;YAEd,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,QAAQ,qBAAqB;YAClE,MAAM,SAAS,GAAG,KAAK,aAAa,OAAO,CAAC,CAAC;YAC7C,MAAM,SAAS,GAAG,KAAK,aAAa,OAAO,CAAC,CAAC;YAE7C,IAAI,GAAG;gBACL,aAAa,OAAO,GAAG;oBAAE,GAAG;oBAAQ,GAAG;gBAAO;YAChD;YAEA,MAAM,SAAS;gBAAC,OAAO,QAAQ;gBAAK,MAAM,SAAS;aAAI;YACvD,MAAM,qBAAqB,KAAK,KAAK,CACnC,SAAS,MAAM,CAAC,EAAE,EAClB,SAAS,MAAM,CAAC,EAAE;YAEpB,MAAM,iBAAiB,MAAM,KAAK,GAAG,CAAC,OAAO,UAAU;YAEvD,IAAI,qBAAqB,gBAAgB;gBACvC,QAAQ,KAAK,CAAC,WAAW,CAAC,YAAY;gBACtC;YACF;YAEA,MAAM,WACJ,SAAS,OAAO,aAChB,SAAS,OAAO,QAAQ,aACxB,SAAS,MAAM,aACf,SAAS,MAAM,SAAS;YAE1B,QAAQ,KAAK,CAAC,WAAW,CAAC,YAAY,WAAW,MAAM;YAEvD,IAAI,CAAC,UAAU;YAEf,MAAM,eACJ,WAAW,QAAQ,KAAK,CAAC,gBAAgB,CAAC,eAAe;YAC3D,IAAI,cACF,AAAC,MAAM,KAAK,KAAK,CAAC,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,MAAM,CAAC,EAAE,IACtD,KAAK,EAAE,GACT;YAEF,MAAM,YAAY,AAAC,CAAC,cAAc,eAAe,GAAG,IAAI,MAAO;YAC/D,MAAM,WAAW,eAAe;YAEhC,CAAA,GAAA,gLAAA,CAAA,UAAO,AAAD,EAAE,cAAc,UAAU;gBAC9B,UAAU;gBACV,MAAM;oBAAC;oBAAM;oBAAG;oBAAK;iBAAE;gBACvB,UAAU,CAAC;oBACT,QAAQ,KAAK,CAAC,WAAW,CAAC,WAAW,OAAO;gBAC9C;YACF;QACF;IACF,GACA;QAAC;QAAc;QAAW;KAAiB;IAG7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;QAEd,MAAM,eAAe,IAAM;QAC3B,MAAM,oBAAoB,CAAC,IAAoB,WAAW;QAE1D,OAAO,gBAAgB,CAAC,UAAU,cAAc;YAAE,SAAS;QAAK;QAChE,SAAS,IAAI,CAAC,gBAAgB,CAAC,eAAe,mBAAmB;YAC/D,SAAS;QACX;QAEA,OAAO;YACL,IAAI,kBAAkB,OAAO,EAAE;gBAC7B,qBAAqB,kBAAkB,OAAO;YAChD;YACA,OAAO,mBAAmB,CAAC,UAAU;YACrC,SAAS,IAAI,CAAC,mBAAmB,CAAC,eAAe;QACnD;IACF,GAAG;QAAC;QAAY;KAAS;IAEzB,qBACE;;0BACE,8OAAC;gBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,uGACA,QAAQ,eACR,oBACA,YAAY;;;;;;0BAGhB,8OAAC;gBACC,KAAK;gBACL,OACE;oBACE,UAAU,GAAG,KAAK,EAAE,CAAC;oBACrB,YAAY;oBACZ,WAAW;oBACX,YAAY;oBACZ,gCAAgC,GAAG,YAAY,EAAE,CAAC;oBAClD,oCAAoC;oBACpC,cAAc,CAAC;;;;;;;;;;;iBAWZ,CAAC;gBACN;gBAEF,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yFACA,QAAQ,eACR,OAAO,KAAK,uBACZ,WACA,YAAY;0BAGd,cAAA,8OAAC;oBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,QACA,qBACA,sHACA,sEACA,0EACA,6EACA,4CACA,oCACA;;;;;;;;;;;;;AAMZ;AAGF,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 217, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/word-pull-up.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion, Variants } from \"framer-motion\";\n\nimport { cn } from \"@/lib/utils\";\n\ninterface WordPullUpProps {\n  words: string;\n  delayMultiple?: number;\n  wrapperFramerProps?: Variants;\n  framerProps?: Variants;\n  className?: string;\n}\n\nfunction WordPullUp({\n  words,\n  wrapperFramerProps = {\n    hidden: { opacity: 0 },\n    show: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2,\n      },\n    },\n  },\n  framerProps = {\n    hidden: { y: 20, opacity: 0 },\n    show: { y: 0, opacity: 1 },\n  },\n  className,\n}: WordPullUpProps) {\n  return (\n    <motion.h1\n      variants={wrapperFramerProps}\n      initial=\"hidden\"\n      animate=\"show\"\n      className={cn(\n        \"font-display text-center text-4xl font-bold leading-[5rem] tracking-[-0.02em] drop-shadow-sm text-white\",\n        className,\n      )}\n    >\n      {words.split(\" \").map((word, i) => (\n        <motion.span\n          key={i}\n          variants={framerProps}\n          style={{ display: \"inline-block\", paddingRight: \"8px\" }}\n        >\n          {word === \"\" ? <span>&nbsp;</span> : word}\n        </motion.span>\n      ))}\n    </motion.h1>\n  );\n}\n\nexport { WordPullUp };\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;AAcA,SAAS,WAAW,EAClB,KAAK,EACL,qBAAqB;IACnB,QAAQ;QAAE,SAAS;IAAE;IACrB,MAAM;QACJ,SAAS;QACT,YAAY;YACV,iBAAiB;QACnB;IACF;AACF,CAAC,EACD,cAAc;IACZ,QAAQ;QAAE,GAAG;QAAI,SAAS;IAAE;IAC5B,MAAM;QAAE,GAAG;QAAG,SAAS;IAAE;AAC3B,CAAC,EACD,SAAS,EACO;IAChB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;QACR,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,2GACA;kBAGD,MAAM,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,MAAM,kBAC3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gBAEV,UAAU;gBACV,OAAO;oBAAE,SAAS;oBAAgB,cAAc;gBAAM;0BAErD,SAAS,mBAAK,8OAAC;8BAAK;;;;;2BAAgB;eAJhC;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 281, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/marquee-effect.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useRef } from \"react\";\nimport {\n  motion,\n  useScroll,\n  useSpring,\n  useTransform,\n  useMotionValue,\n  useVelocity,\n  useAnimationFrame,\n} from \"framer-motion\";\nimport { wrap } from \"@motionone/utils\";\nimport { cn } from \"@/lib/utils\";\n\ntype MarqueeAnimationProps = {\n  children: string;\n  className?: string;\n  direction?: \"left\" | \"right\";\n  baseVelocity: number;\n};\n\nfunction MarqueeAnimation({\n  children,\n  className,\n  direction = \"left\",\n  baseVelocity = 10,\n}: MarqueeAnimationProps) {\n  const baseX = useMotionValue(0);\n  const { scrollY } = useScroll();\n  const scrollVelocity = useVelocity(scrollY);\n  const smoothVelocity = useSpring(scrollVelocity, {\n    damping: 50,\n    stiffness: 400,\n  });\n  const velocityFactor = useTransform(smoothVelocity, [0, 1000], [0, 0], {\n    clamp: false,\n  });\n\n  const x = useTransform(baseX, (v) => `${wrap(-20, -45, v)}%`);\n\n  const directionFactor = useRef<number>(1);\n  useAnimationFrame((t, delta) => {\n    let moveBy = directionFactor.current * baseVelocity * (delta / 1000);\n\n    if (direction == \"left\") {\n      directionFactor.current = 1;\n    } else if (direction == \"right\") {\n      directionFactor.current = -1;\n    }\n\n    moveBy += directionFactor.current * moveBy * velocityFactor.get();\n\n    baseX.set(baseX.get() + moveBy);\n  });\n\n  return (\n    <div className=\"overflow-hidden max-w-[100vw] text-nowrap flex-nowrap flex relative\">\n      <motion.div\n        className={cn(\n          \"font-bold uppercase text-5xl flex flex-nowrap text-nowrap *:block *:me-10 text-white drop-shadow-lg\",\n          className\n        )}\n        style={{ x }}\n      >\n        <span>{children}</span>\n        <span>{children}</span>\n        <span>{children}</span>\n        <span>{children}</span>\n      </motion.div>\n    </div>\n  );\n}\n\nexport { MarqueeAnimation };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AAbA;;;;;;AAsBA,SAAS,iBAAiB,EACxB,QAAQ,EACR,SAAS,EACT,YAAY,MAAM,EAClB,eAAe,EAAE,EACK;IACtB,MAAM,QAAQ,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE;IAC7B,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD;IAC5B,MAAM,iBAAiB,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;IACnC,MAAM,iBAAiB,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB;QAC/C,SAAS;QACT,WAAW;IACb;IACA,MAAM,iBAAiB,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB;QAAC;QAAG;KAAK,EAAE;QAAC;QAAG;KAAE,EAAE;QACrE,OAAO;IACT;IAEA,MAAM,IAAI,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,OAAO,CAAC,IAAM,GAAG,CAAA,GAAA,0JAAA,CAAA,OAAI,AAAD,EAAE,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;IAE5D,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAU;IACvC,CAAA,GAAA,qLAAA,CAAA,oBAAiB,AAAD,EAAE,CAAC,GAAG;QACpB,IAAI,SAAS,gBAAgB,OAAO,GAAG,eAAe,CAAC,QAAQ,IAAI;QAEnE,IAAI,aAAa,QAAQ;YACvB,gBAAgB,OAAO,GAAG;QAC5B,OAAO,IAAI,aAAa,SAAS;YAC/B,gBAAgB,OAAO,GAAG,CAAC;QAC7B;QAEA,UAAU,gBAAgB,OAAO,GAAG,SAAS,eAAe,GAAG;QAE/D,MAAM,GAAG,CAAC,MAAM,GAAG,KAAK;IAC1B;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,uGACA;YAEF,OAAO;gBAAE;YAAE;;8BAEX,8OAAC;8BAAM;;;;;;8BACP,8OAAC;8BAAM;;;;;;8BACP,8OAAC;8BAAM;;;;;;8BACP,8OAAC;8BAAM;;;;;;;;;;;;;;;;;AAIf", "debugId": null}}]}