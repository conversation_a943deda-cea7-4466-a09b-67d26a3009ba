import { <PERSON><PERSON> } from "@/components/ui/button";
import { BackgroundBeams } from "@/components/ui/background-beams";
import { Ben<PERSON><PERSON>ard, BentoGrid } from "@/components/ui/bento-grid";
import ShinyButton from "@/components/ui/shiny-button";
import { LayoutTemplate, FileText, Users, BarChart2, Palette, Zap, Shield, Globe, Search, Calendar, Sparkles } from "lucide-react";

export default function Features() {
  return (
    <div className="min-h-screen relative overflow-hidden">
      <BackgroundBeams />

      {/* Hero Section */}
      <section className="relative z-10 w-full px-4 py-20 pt-32">
        <div className="text-center max-w-6xl mx-auto">
          <h1 className="text-4xl md:text-6xl font-black mb-6 leading-none text-white drop-shadow-lg">
            Powerful Features for Your Business
          </h1>
          <p className="text-xl md:text-2xl text-white/90 mb-8 leading-relaxed max-w-4xl mx-auto drop-shadow-sm">
            Everything you need to build, manage, and grow your business in one integrated platform.
          </p>
        </div>
      </section>

      {/* Features Bento Grid */}
      <section className="relative z-10 py-20 border-t border-white/20">
        <div className="container mx-auto px-4">
          <BentoGrid className="lg:grid-rows-3 max-w-6xl mx-auto min-h-[800px]">
            {[
              {
                Icon: LayoutTemplate,
                name: "Website Builder",
                description: "Create stunning websites with our drag-and-drop builder. No coding required.",
                href: "/contact",
                cta: "Contact Us",
                background: <div className="absolute -right-20 -top-20 opacity-60 w-40 h-40 bg-gradient-to-br from-primary/20 to-primary/5 rounded-full blur-3xl" />,
                className: "lg:row-start-1 lg:row-end-4 lg:col-start-2 lg:col-end-3",
              },
              {
                Icon: FileText,
                name: "Smart Invoicing",
                description: "Generate professional invoices and get paid faster with automated reminders.",
                href: "/contact",
                cta: "Contact Us",
                background: <div className="absolute -right-20 -top-20 opacity-60 w-40 h-40 bg-gradient-to-br from-primary/20 to-primary/5 rounded-full blur-3xl" />,
                className: "lg:col-start-1 lg:col-end-2 lg:row-start-1 lg:row-end-3",
              },
              {
                Icon: Users,
                name: "CRM System",
                description: "Manage customer relationships and track leads through your sales pipeline.",
                href: "/contact",
                cta: "Contact Us",
                background: <div className="absolute -right-20 -top-20 opacity-60 w-40 h-40 bg-gradient-to-br from-primary/20 to-primary/5 rounded-full blur-3xl" />,
                className: "lg:col-start-1 lg:col-end-2 lg:row-start-3 lg:row-end-4",
              },
              {
                Icon: BarChart2,
                name: "Analytics Dashboard",
                description: "Get insights into your business performance with real-time analytics.",
                href: "/contact",
                cta: "Contact Us",
                background: <div className="absolute -right-20 -top-20 opacity-60 w-40 h-40 bg-gradient-to-br from-primary/20 to-primary/5 rounded-full blur-3xl" />,
                className: "lg:col-start-3 lg:col-end-3 lg:row-start-1 lg:row-end-2",
              },
              {
                Icon: Palette,
                name: "Brand Management",
                description: "Maintain consistent branding across all your business touchpoints.",
                href: "/contact",
                cta: "Contact Us",
                background: <div className="absolute -right-20 -top-20 opacity-60 w-40 h-40 bg-gradient-to-br from-primary/20 to-primary/5 rounded-full blur-3xl" />,
                className: "lg:col-start-3 lg:col-end-3 lg:row-start-2 lg:row-end-4",
              },
            ].map((feature) => (
              <BentoCard key={feature.name} {...feature} />
            ))}
          </BentoGrid>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative z-10 py-20 border-t border-white/20">
        <div className="container mx-auto px-4">
          <div className="mx-auto flex max-w-[58rem] flex-col items-center space-y-4 text-center">
            <h2 className="font-heading text-3xl leading-[1.1] sm:text-3xl md:text-6xl text-white drop-shadow-lg">
              Ready to transform your business?
            </h2>
            <p className="max-w-[42rem] leading-normal text-white/90 sm:text-xl sm:leading-8 drop-shadow-sm">
              Join beauty entrepreneurs and business owners who trust Vierla to power their business operations.
            </p>
            <div className="space-x-4">
              <ShinyButton asChild>
                <a href="/apply">Get Started Free</a>
              </ShinyButton>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
