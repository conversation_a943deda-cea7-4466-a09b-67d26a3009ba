{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/background-beams.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const BackgroundBeams = registerClientReference(\n    function() { throw new Error(\"Attempted to call BackgroundBeams() from the server but BackgroundBeams is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/background-beams.tsx <module evaluation>\",\n    \"BackgroundBeams\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,oEACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/background-beams.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const BackgroundBeams = registerClientReference(\n    function() { throw new Error(\"Attempted to call BackgroundBeams() from the server but BackgroundBeams is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/background-beams.tsx\",\n    \"BackgroundBeams\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,gDACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/bento-grid.tsx"], "sourcesContent": ["import { ReactNode } from \"react\";\nimport { ArrowRightIcon } from \"@radix-ui/react-icons\";\nimport Link from \"next/link\";\n\nimport { cn } from \"@/lib/utils\";\nimport { Button } from \"@/components/ui/button\";\n\nconst BentoGrid = ({\n  children,\n  className,\n}: {\n  children: ReactNode;\n  className?: string;\n}) => {\n  return (\n    <div\n      className={cn(\n        \"grid w-full auto-rows-[20rem] grid-cols-3 gap-4\",\n        className,\n      )}\n    >\n      {children}\n    </div>\n  );\n};\n\nconst BentoCard = ({\n  name,\n  className,\n  background,\n  Icon,\n  description,\n  href,\n  cta,\n}: {\n  name: string;\n  className: string;\n  background: ReactNode;\n  Icon: any;\n  description: string;\n  href: string;\n  cta: string;\n}) => (\n  <div\n    key={name}\n    className={cn(\n      \"group relative col-span-3 flex flex-col justify-between overflow-hidden rounded-xl\",\n      // Updated styles for our theme\n      \"bg-white/10 backdrop-blur-md border border-white/20 shadow-xl\",\n      // dark styles\n      \"transform-gpu dark:bg-black/20 dark:[border:1px_solid_rgba(255,255,255,.2)] dark:[box-shadow:0_-20px_80px_-20px_#ffffff1f_inset]\",\n      className,\n    )}\n  >\n    <div>{background}</div>\n    <div className=\"pointer-events-none z-10 flex transform-gpu flex-col gap-1 p-6 transition-all duration-300 group-hover:-translate-y-10\">\n      <Icon className=\"h-12 w-12 origin-left transform-gpu text-white drop-shadow-lg transition-all duration-300 ease-in-out group-hover:scale-75\" />\n      <h3 className=\"text-xl font-semibold text-white drop-shadow-lg\">\n        {name}\n      </h3>\n      <p className=\"max-w-lg text-white/80 drop-shadow-sm\">{description}</p>\n    </div>\n\n    <div\n      className={cn(\n        \"pointer-events-none absolute bottom-0 flex w-full translate-y-10 transform-gpu flex-row items-center p-4 opacity-0 transition-all duration-300 group-hover:translate-y-0 group-hover:opacity-100\",\n      )}\n    >\n      <Button variant=\"ghost\" asChild size=\"sm\" className=\"pointer-events-auto text-white hover:bg-white/10 hover:text-white border border-white/30 rounded-full\">\n        <Link href={href}>\n          {cta}\n          <ArrowRightIcon className=\"ml-2 h-4 w-4\" />\n        </Link>\n      </Button>\n    </div>\n    <div className=\"pointer-events-none absolute inset-0 transform-gpu transition-all duration-300 group-hover:bg-primary/[.03] group-hover:dark:bg-primary/10\" />\n  </div>\n);\n\nexport { BentoCard, BentoGrid };\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;AACA;;;;;;AAEA,MAAM,YAAY,CAAC,EACjB,QAAQ,EACR,SAAS,EAIV;IACC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mDACA;kBAGD;;;;;;AAGP;AAEA,MAAM,YAAY,CAAC,EACjB,IAAI,EACJ,SAAS,EACT,UAAU,EACV,IAAI,EACJ,WAAW,EACX,IAAI,EACJ,GAAG,EASJ,iBACC,8OAAC;QAEC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sFACA,+BAA+B;QAC/B,iEACA,cAAc;QACd,oIACA;;0BAGF,8OAAC;0BAAK;;;;;;0BACN,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;;;;;;kCAChB,8OAAC;wBAAG,WAAU;kCACX;;;;;;kCAEH,8OAAC;wBAAE,WAAU;kCAAyC;;;;;;;;;;;;0BAGxD,8OAAC;gBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV;0BAGF,cAAA,8OAAC,2HAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,OAAO;oBAAC,MAAK;oBAAK,WAAU;8BAClD,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAM;;4BACT;0CACD,8OAAC,gLAAA,CAAA,iBAAc;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAIhC,8OAAC;gBAAI,WAAU;;;;;;;OA/BV", "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/app/features/page.tsx"], "sourcesContent": ["import { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { BackgroundBeams } from \"@/components/ui/background-beams\";\nimport { Ben<PERSON><PERSON>ard, BentoGrid } from \"@/components/ui/bento-grid\";\nimport ShinyButton from \"@/components/ui/shiny-button\";\nimport { LayoutTemplate, FileText, Users, BarChart2, Palette, Zap, Shield, Globe, Search, Calendar, Sparkles } from \"lucide-react\";\n\nexport default function Features() {\n  return (\n    <div className=\"min-h-screen relative overflow-hidden\">\n      <BackgroundBeams />\n\n      {/* Hero Section */}\n      <section className=\"relative z-10 w-full px-4 py-20 pt-32\">\n        <div className=\"text-center max-w-6xl mx-auto\">\n          <h1 className=\"text-4xl md:text-6xl font-black mb-6 leading-none text-white drop-shadow-lg\">\n            Powerful Features for Your Business\n          </h1>\n          <p className=\"text-xl md:text-2xl text-white/90 mb-8 leading-relaxed max-w-4xl mx-auto drop-shadow-sm\">\n            Everything you need to build, manage, and grow your business in one integrated platform.\n          </p>\n        </div>\n      </section>\n\n      {/* Features Bento Grid */}\n      <section className=\"relative z-10 py-20 border-t border-white/20\">\n        <div className=\"container mx-auto px-4\">\n          <BentoGrid className=\"lg:grid-rows-3 max-w-6xl mx-auto min-h-[800px]\">\n            {[\n              {\n                Icon: LayoutTemplate,\n                name: \"Website Builder\",\n                description: \"Create stunning websites with our drag-and-drop builder. No coding required.\",\n                href: \"/contact\",\n                cta: \"Contact Us\",\n                background: <div className=\"absolute -right-20 -top-20 opacity-60 w-40 h-40 bg-gradient-to-br from-primary/20 to-primary/5 rounded-full blur-3xl\" />,\n                className: \"lg:row-start-1 lg:row-end-4 lg:col-start-2 lg:col-end-3\",\n              },\n              {\n                Icon: FileText,\n                name: \"Smart Invoicing\",\n                description: \"Generate professional invoices and get paid faster with automated reminders.\",\n                href: \"/contact\",\n                cta: \"Contact Us\",\n                background: <div className=\"absolute -right-20 -top-20 opacity-60 w-40 h-40 bg-gradient-to-br from-primary/20 to-primary/5 rounded-full blur-3xl\" />,\n                className: \"lg:col-start-1 lg:col-end-2 lg:row-start-1 lg:row-end-3\",\n              },\n              {\n                Icon: Users,\n                name: \"CRM System\",\n                description: \"Manage customer relationships and track leads through your sales pipeline.\",\n                href: \"/contact\",\n                cta: \"Contact Us\",\n                background: <div className=\"absolute -right-20 -top-20 opacity-60 w-40 h-40 bg-gradient-to-br from-primary/20 to-primary/5 rounded-full blur-3xl\" />,\n                className: \"lg:col-start-1 lg:col-end-2 lg:row-start-3 lg:row-end-4\",\n              },\n              {\n                Icon: BarChart2,\n                name: \"Analytics Dashboard\",\n                description: \"Get insights into your business performance with real-time analytics.\",\n                href: \"/contact\",\n                cta: \"Contact Us\",\n                background: <div className=\"absolute -right-20 -top-20 opacity-60 w-40 h-40 bg-gradient-to-br from-primary/20 to-primary/5 rounded-full blur-3xl\" />,\n                className: \"lg:col-start-3 lg:col-end-3 lg:row-start-1 lg:row-end-2\",\n              },\n              {\n                Icon: Palette,\n                name: \"Brand Management\",\n                description: \"Maintain consistent branding across all your business touchpoints.\",\n                href: \"/contact\",\n                cta: \"Contact Us\",\n                background: <div className=\"absolute -right-20 -top-20 opacity-60 w-40 h-40 bg-gradient-to-br from-primary/20 to-primary/5 rounded-full blur-3xl\" />,\n                className: \"lg:col-start-3 lg:col-end-3 lg:row-start-2 lg:row-end-4\",\n              },\n            ].map((feature) => (\n              <BentoCard key={feature.name} {...feature} />\n            ))}\n          </BentoGrid>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"relative z-10 py-20 border-t border-white/20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"mx-auto flex max-w-[58rem] flex-col items-center space-y-4 text-center\">\n            <h2 className=\"font-heading text-3xl leading-[1.1] sm:text-3xl md:text-6xl text-white drop-shadow-lg\">\n              Ready to transform your business?\n            </h2>\n            <p className=\"max-w-[42rem] leading-normal text-white/90 sm:text-xl sm:leading-8 drop-shadow-sm\">\n              Join beauty entrepreneurs and business owners who trust Vierla to power their business operations.\n            </p>\n            <div className=\"space-x-4\">\n              <ShinyButton asChild>\n                <a href=\"/apply\">Get Started Free</a>\n              </ShinyButton>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,wIAAA,CAAA,kBAAe;;;;;0BAGhB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA8E;;;;;;sCAG5F,8OAAC;4BAAE,WAAU;sCAA0F;;;;;;;;;;;;;;;;;0BAO3G,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kIAAA,CAAA,YAAS;wBAAC,WAAU;kCAClB;4BACC;gCACE,MAAM,0NAAA,CAAA,iBAAc;gCACpB,MAAM;gCACN,aAAa;gCACb,MAAM;gCACN,KAAK;gCACL,0BAAY,8OAAC;oCAAI,WAAU;;;;;;gCAC3B,WAAW;4BACb;4BACA;gCACE,MAAM,8MAAA,CAAA,WAAQ;gCACd,MAAM;gCACN,aAAa;gCACb,MAAM;gCACN,KAAK;gCACL,0BAAY,8OAAC;oCAAI,WAAU;;;;;;gCAC3B,WAAW;4BACb;4BACA;gCACE,MAAM,oMAAA,CAAA,QAAK;gCACX,MAAM;gCACN,aAAa;gCACb,MAAM;gCACN,KAAK;gCACL,0BAAY,8OAAC;oCAAI,WAAU;;;;;;gCAC3B,WAAW;4BACb;4BACA;gCACE,MAAM,gOAAA,CAAA,YAAS;gCACf,MAAM;gCACN,aAAa;gCACb,MAAM;gCACN,KAAK;gCACL,0BAAY,8OAAC;oCAAI,WAAU;;;;;;gCAC3B,WAAW;4BACb;4BACA;gCACE,MAAM,wMAAA,CAAA,UAAO;gCACb,MAAM;gCACN,aAAa;gCACb,MAAM;gCACN,KAAK;gCACL,0BAAY,8OAAC;oCAAI,WAAU;;;;;;gCAC3B,WAAW;4BACb;yBACD,CAAC,GAAG,CAAC,CAAC,wBACL,8OAAC,kIAAA,CAAA,YAAS;gCAAqB,GAAG,OAAO;+BAAzB,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;0BAOpC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwF;;;;;;0CAGtG,8OAAC;gCAAE,WAAU;0CAAoF;;;;;;0CAGjG,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oIAAA,CAAA,UAAW;oCAAC,OAAO;8CAClB,cAAA,8OAAC;wCAAE,MAAK;kDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjC", "debugId": null}}]}