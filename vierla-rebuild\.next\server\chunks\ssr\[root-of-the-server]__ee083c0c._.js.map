{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/background-beams.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const BackgroundBeams = registerClientReference(\n    function() { throw new Error(\"Attempted to call BackgroundBeams() from the server but BackgroundBeams is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/background-beams.tsx <module evaluation>\",\n    \"BackgroundBeams\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,oEACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/background-beams.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const BackgroundBeams = registerClientReference(\n    function() { throw new Error(\"Attempted to call BackgroundBeams() from the server but BackgroundBeams is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/background-beams.tsx\",\n    \"BackgroundBeams\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,gDACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/golden-glow-card-with-margin.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const GoldenGlowCardWithMargin = registerClientReference(\n    function() { throw new Error(\"Attempted to call GoldenGlowCardWithMargin() from the server but GoldenGlowCardWithMargin is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/golden-glow-card-with-margin.tsx <module evaluation>\",\n    \"GoldenGlowCardWithMargin\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ui/golden-glow-card-with-margin.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/golden-glow-card-with-margin.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,2BAA2B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1D;IAAa,MAAM,IAAI,MAAM;AAAgQ,GAC7R,gFACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkT,GAC/U,gFACA", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/golden-glow-card-with-margin.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const GoldenGlowCardWithMargin = registerClientReference(\n    function() { throw new Error(\"Attempted to call GoldenGlowCardWithMargin() from the server but GoldenGlowCardWithMargin is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/golden-glow-card-with-margin.tsx\",\n    \"GoldenGlowCardWithMargin\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ui/golden-glow-card-with-margin.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/golden-glow-card-with-margin.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,2BAA2B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1D;IAAa,MAAM,IAAI,MAAM;AAAgQ,GAC7R,4DACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8R,GAC3T,4DACA", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/new-shiny-button.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const NewShinyButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call NewShinyButton() from the server but NewShinyButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/new-shiny-button.tsx <module evaluation>\",\n    \"NewShinyButton\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ui/new-shiny-button.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/new-shiny-button.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,oEACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/new-shiny-button.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const NewShinyButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call NewShinyButton() from the server but NewShinyButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/new-shiny-button.tsx\",\n    \"NewShinyButton\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ui/new-shiny-button.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/new-shiny-button.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,gDACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkR,GAC/S,gDACA", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/word-pull-up.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const WordPullUp = registerClientReference(\n    function() { throw new Error(\"Attempted to call WordPullUp() from the server but WordPullUp is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/word-pull-up.tsx <module evaluation>\",\n    \"WordPullUp\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,gEACA", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/word-pull-up.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const WordPullUp = registerClientReference(\n    function() { throw new Error(\"Attempted to call WordPullUp() from the server but WordPullUp is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/word-pull-up.tsx\",\n    \"WordPullUp\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,4CACA", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/marquee-effect.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const MarqueeAnimation = registerClientReference(\n    function() { throw new Error(\"Attempted to call MarqueeAnimation() from the server but MarqueeAnimation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/marquee-effect.tsx <module evaluation>\",\n    \"MarqueeAnimation\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,kEACA", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/marquee-effect.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const MarqueeAnimation = registerClientReference(\n    function() { throw new Error(\"Attempted to call MarqueeAnimation() from the server but MarqueeAnimation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/marquee-effect.tsx\",\n    \"MarqueeAnimation\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,8CACA", "debugId": null}}, {"offset": {"line": 186, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 194, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/app/page.tsx"], "sourcesContent": ["import { BackgroundBeams } from \"@/components/ui/background-beams\";\nimport { GoldenGlowCardWithMargin } from \"@/components/ui/golden-glow-card-with-margin\";\nimport { NewShinyButton } from \"@/components/ui/new-shiny-button\";\nimport { WordPullUp } from \"@/components/ui/word-pull-up\";\nimport { MarqueeAnimation } from \"@/components/ui/marquee-effect\";\nimport { LayoutTemplate, Search, Calendar, Sparkles } from \"lucide-react\";\nimport Link from \"next/link\";\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen relative overflow-hidden\">\n      <BackgroundBeams />\n\n      {/* Hero Section */}\n      <section className=\"relative z-10 w-full px-4 py-20 pt-32\">\n        <div className=\"text-center max-w-6xl mx-auto\">\n          {/* Word Pull Up for main heading */}\n          <div className=\"mb-8\">\n            <WordPullUp\n              words=\"Self-Care, Simplified\"\n              className=\"text-6xl md:text-8xl font-black leading-none text-white drop-shadow-lg\"\n            />\n          </div>\n\n          {/* Word Pull Up for description */}\n          <div className=\"mb-8\">\n            <WordPullUp\n              words=\"The ultimate marketplace connecting you with top beauty professionals, and the all-in-one tool for providers to manage and grow their business.\"\n              className=\"text-xl md:text-2xl text-white/90 leading-relaxed font-light max-w-4xl mx-auto drop-shadow-sm\"\n            />\n          </div>\n\n          {/* Marquee effect for launching soon - moved below description with larger text */}\n          <div className=\"mb-12 overflow-hidden\">\n            <MarqueeAnimation\n              direction=\"left\"\n              baseVelocity={-1}\n              className=\"text-white/80 text-3xl md:text-4xl font-bold drop-shadow-lg bg-transparent py-4\"\n            >\n              Launching Soon in Toronto & Ottawa - Ontario's Premier Beauty Marketplace\n            </MarqueeAnimation>\n          </div>\n\n          {/* Dual Call-to-Action Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-6 justify-center items-center mb-16\">\n            {/* Commented out Find Your Perfect Stylist button */}\n            {/* <Link\n              href=\"/customer-app\"\n              className=\"group flex items-center px-8 py-4 rounded-full font-medium transition-all duration-300 text-lg hover:scale-105 min-w-[280px] justify-center bg-primary text-primary-foreground\"\n            >\n              <Search className=\"mr-3 w-6 h-6\" />\n              Find Your Perfect Stylist\n              <Sparkles className=\"ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform\" />\n            </Link> */}\n\n            <Link href=\"/provider-app\">\n              <NewShinyButton className=\"group flex items-center px-8 py-4 min-w-[280px] justify-center text-lg font-medium\">\n                <LayoutTemplate className=\"mr-3 w-6 h-6\" />\n                Grow Your Business\n                <svg className=\"ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                </svg>\n              </NewShinyButton>\n            </Link>\n          </div>\n        </div>\n      </section>\n\n      {/* How It Works Section */}\n      <section className=\"relative z-10 py-20 border-t border-white/20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-5xl font-black text-white mb-6 drop-shadow-lg\">\n              How It Works\n            </h2>\n            <p className=\"text-xl text-white/90 max-w-3xl mx-auto drop-shadow-sm\">\n              Getting beautiful has never been this simple\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8 max-w-6xl mx-auto\">\n            {/* 1. Discover */}\n            <GoldenGlowCardWithMargin>\n              <div className=\"p-6 text-center h-full flex flex-col justify-center\">\n                <div className=\"w-16 h-16 md:w-20 md:h-20 mx-auto mb-4 md:mb-6 rounded-full flex items-center justify-center bg-white/20 border-2 border-white/30\">\n                  <Search className=\"w-8 h-8 md:w-10 md:h-10 text-white\" />\n                </div>\n                <h3 className=\"text-lg md:text-2xl font-bold text-white mb-2 md:mb-4 drop-shadow-lg\">1. Discover</h3>\n                <p className=\"text-white/80 leading-relaxed text-sm md:text-base drop-shadow-md\">\n                  Explore our curated network of top-rated beauty professionals in your area.\n                </p>\n              </div>\n            </GoldenGlowCardWithMargin>\n\n            {/* 2. Book */}\n            <GoldenGlowCardWithMargin>\n              <div className=\"p-6 text-center h-full flex flex-col justify-center\">\n                <div className=\"w-16 h-16 md:w-20 md:h-20 mx-auto mb-4 md:mb-6 rounded-full flex items-center justify-center bg-white/20 border-2 border-white/30\">\n                  <Calendar className=\"w-8 h-8 md:w-10 md:h-10 text-white\" />\n                </div>\n                <h3 className=\"text-lg md:text-2xl font-bold text-white mb-2 md:mb-4 drop-shadow-lg\">2. Book</h3>\n                <p className=\"text-white/80 leading-relaxed text-sm md:text-base drop-shadow-md\">\n                  Select your service, choose a time that works for you, and pay securely online.\n                </p>\n              </div>\n            </GoldenGlowCardWithMargin>\n\n            {/* 3. Pay */}\n            <GoldenGlowCardWithMargin>\n              <div className=\"p-6 text-center h-full flex flex-col justify-center\">\n                <div className=\"w-16 h-16 md:w-20 md:h-20 mx-auto mb-4 md:mb-6 rounded-full flex items-center justify-center bg-white/20 border-2 border-white/30\">\n                  <svg className=\"w-8 h-8 md:w-10 md:h-10 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z\" />\n                  </svg>\n                </div>\n                <h3 className=\"text-lg md:text-2xl font-bold text-white mb-2 md:mb-4 drop-shadow-lg\">3. Pay</h3>\n                <p className=\"text-white/80 leading-relaxed text-sm md:text-base drop-shadow-md\">\n                  Secure payment processing with multiple payment options for your convenience.\n                </p>\n              </div>\n            </GoldenGlowCardWithMargin>\n\n            {/* 4. Relax */}\n            <GoldenGlowCardWithMargin>\n              <div className=\"p-6 text-center h-full flex flex-col justify-center\">\n                <div className=\"w-16 h-16 md:w-20 md:h-20 mx-auto mb-4 md:mb-6 rounded-full flex items-center justify-center bg-white/20 border-2 border-white/30\">\n                  <Sparkles className=\"w-8 h-8 md:w-10 md:h-10 text-white\" />\n                </div>\n                <h3 className=\"text-lg md:text-2xl font-bold text-white mb-2 md:mb-4 drop-shadow-lg\">4. Relax</h3>\n                <p className=\"text-white/80 leading-relaxed text-sm md:text-base drop-shadow-md\">\n                  Your vetted professional comes to you, ready to provide an exceptional service.\n                </p>\n              </div>\n            </GoldenGlowCardWithMargin>\n          </div>\n        </div>\n      </section>\n\n      {/* Featured Services Section */}\n      <section className=\"relative z-10 py-20 border-t border-white/20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-5xl font-black text-white mb-6 drop-shadow-lg\">Featured Services</h2>\n            <p className=\"text-xl text-white/90 max-w-4xl mx-auto drop-shadow-sm\">\n              Discover our curated selection of premium beauty services. Choose to have professionals come to you, or visit their studios.\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 max-w-7xl mx-auto\">\n            {[\n              {\n                title: \"Barbers\",\n                services: [\"• Classic cuts\", \"• Beard trims\", \"• Hot towel shaves\", \"• Hair styling\"],\n              },\n              {\n                title: \"Makeup\",\n                services: [\"• Event makeup\", \"• Bridal looks\", \"• Fashion makeup\", \"• Everyday glam\"],\n              },\n              {\n                title: \"Salons\",\n                services: [\"• Hair cuts & color\", \"• Blowouts\", \"• Treatments\", \"• Full styling\"],\n              },\n              {\n                title: \"Locs\",\n                services: [\"• Loc maintenance\", \"• Retwisting\", \"• Loc styling\", \"• Loc repair\"],\n              },\n              {\n                title: \"Braids\",\n                services: [\"• Box braids\", \"• Cornrows\", \"• French braids\", \"• Protective styles\"],\n                mobileHidden: true,\n              },\n              {\n                title: \"Nails\",\n                services: [\"• Manicures\", \"• Pedicures\", \"• Nail art\", \"• Gel polish\"],\n                mobileHidden: true,\n              },\n              {\n                title: \"Skincare\",\n                services: [\"• Facials\", \"• Chemical peels\", \"• Microdermabrasion\", \"• Anti-aging\"],\n                mobileHidden: true,\n              },\n              {\n                title: \"Massage\",\n                services: [\"• Relaxation\", \"• Deep tissue\", \"• Hot stone\", \"• Aromatherapy\"],\n                mobileHidden: true,\n              },\n              {\n                title: \"Brows\",\n                services: [\"• Eyebrow shaping\", \"• Threading\", \"• Tinting\", \"• Microblading\"],\n                mobileHidden: true,\n              },\n              {\n                title: \"Henna\",\n                services: [\"• Traditional henna\", \"• Bridal designs\", \"• Body art\", \"• Custom patterns\"],\n                mobileHidden: true,\n              },\n              {\n                title: \"Waxing\",\n                services: [\"• Full body wax\", \"• Brazilian wax\", \"• Facial wax\", \"• Leg wax\"],\n                mobileHidden: true,\n              },\n              {\n                title: \"Laser Hair Removal\",\n                services: [\"• Full body laser\", \"• Facial laser\", \"• Bikini laser\", \"• Underarm laser\"],\n                mobileHidden: true,\n              },\n            ].map((service, index) => (\n              <div key={index} className={service.mobileHidden ? \"hidden md:block\" : \"\"}>\n                <GoldenGlowCardWithMargin>\n                  <div className=\"p-6 text-center h-full flex flex-col justify-between\">\n                    <div>\n                      <h3 className=\"text-xl font-bold text-white mb-4 drop-shadow-lg\">\n                        {service.title}\n                      </h3>\n                      <div className=\"text-white/80 text-sm leading-relaxed text-left drop-shadow-sm\">\n                        {service.services.map((serviceItem, idx) => (\n                          <div key={idx} className=\"mb-1\">\n                            {serviceItem}\n                          </div>\n                        ))}\n                      </div>\n                    </div>\n                    <div className=\"mt-4\">\n                      <span className=\"text-xs px-3 py-1 rounded-full bg-primary/10 text-primary drop-shadow-sm\">\n                        Coming Soon\n                      </span>\n                    </div>\n                  </div>\n                </GoldenGlowCardWithMargin>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;;;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,wIAAA,CAAA,kBAAe;;;;;0BAGhB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,uIAAA,CAAA,aAAU;gCACT,OAAM;gCACN,WAAU;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,uIAAA,CAAA,aAAU;gCACT,OAAM;gCACN,WAAU;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,sIAAA,CAAA,mBAAgB;gCACf,WAAU;gCACV,cAAc,CAAC;gCACf,WAAU;0CACX;;;;;;;;;;;sCAMH,8OAAC;4BAAI,WAAU;sCAWb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,2IAAA,CAAA,iBAAc;oCAAC,WAAU;;sDACxB,8OAAC,0NAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;wCAAiB;sDAE3C,8OAAC;4CAAI,WAAU;4CAA8D,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACrH,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASjF,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CAGnE,8OAAC;oCAAE,WAAU;8CAAyD;;;;;;;;;;;;sCAKxE,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,6JAAA,CAAA,2BAAwB;8CACvB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,8OAAC;gDAAG,WAAU;0DAAuE;;;;;;0DACrF,8OAAC;gDAAE,WAAU;0DAAoE;;;;;;;;;;;;;;;;;8CAOrF,8OAAC,6JAAA,CAAA,2BAAwB;8CACvB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;0DAEtB,8OAAC;gDAAG,WAAU;0DAAuE;;;;;;0DACrF,8OAAC;gDAAE,WAAU;0DAAoE;;;;;;;;;;;;;;;;;8CAOrF,8OAAC,6JAAA,CAAA,2BAAwB;8CACvB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAqC,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC5F,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,8OAAC;gDAAG,WAAU;0DAAuE;;;;;;0DACrF,8OAAC;gDAAE,WAAU;0DAAoE;;;;;;;;;;;;;;;;;8CAOrF,8OAAC,6JAAA,CAAA,2BAAwB;8CACvB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;0DAEtB,8OAAC;gDAAG,WAAU;0DAAuE;;;;;;0DACrF,8OAAC;gDAAE,WAAU;0DAAoE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU3F,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CACnE,8OAAC;oCAAE,WAAU;8CAAyD;;;;;;;;;;;;sCAKxE,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCACE,OAAO;oCACP,UAAU;wCAAC;wCAAkB;wCAAiB;wCAAsB;qCAAiB;gCACvF;gCACA;oCACE,OAAO;oCACP,UAAU;wCAAC;wCAAkB;wCAAkB;wCAAoB;qCAAkB;gCACvF;gCACA;oCACE,OAAO;oCACP,UAAU;wCAAC;wCAAuB;wCAAc;wCAAgB;qCAAiB;gCACnF;gCACA;oCACE,OAAO;oCACP,UAAU;wCAAC;wCAAqB;wCAAgB;wCAAiB;qCAAe;gCAClF;gCACA;oCACE,OAAO;oCACP,UAAU;wCAAC;wCAAgB;wCAAc;wCAAmB;qCAAsB;oCAClF,cAAc;gCAChB;gCACA;oCACE,OAAO;oCACP,UAAU;wCAAC;wCAAe;wCAAe;wCAAc;qCAAe;oCACtE,cAAc;gCAChB;gCACA;oCACE,OAAO;oCACP,UAAU;wCAAC;wCAAa;wCAAoB;wCAAuB;qCAAe;oCAClF,cAAc;gCAChB;gCACA;oCACE,OAAO;oCACP,UAAU;wCAAC;wCAAgB;wCAAiB;wCAAe;qCAAiB;oCAC5E,cAAc;gCAChB;gCACA;oCACE,OAAO;oCACP,UAAU;wCAAC;wCAAqB;wCAAe;wCAAa;qCAAiB;oCAC7E,cAAc;gCAChB;gCACA;oCACE,OAAO;oCACP,UAAU;wCAAC;wCAAuB;wCAAoB;wCAAc;qCAAoB;oCACxF,cAAc;gCAChB;gCACA;oCACE,OAAO;oCACP,UAAU;wCAAC;wCAAmB;wCAAmB;wCAAgB;qCAAY;oCAC7E,cAAc;gCAChB;gCACA;oCACE,OAAO;oCACP,UAAU;wCAAC;wCAAqB;wCAAkB;wCAAkB;qCAAmB;oCACvF,cAAc;gCAChB;6BACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC;oCAAgB,WAAW,QAAQ,YAAY,GAAG,oBAAoB;8CACrE,cAAA,8OAAC,6JAAA,CAAA,2BAAwB;kDACvB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEACX,QAAQ,KAAK;;;;;;sEAEhB,8OAAC;4DAAI,WAAU;sEACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,aAAa,oBAClC,8OAAC;oEAAc,WAAU;8EACtB;mEADO;;;;;;;;;;;;;;;;8DAMhB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAA2E;;;;;;;;;;;;;;;;;;;;;;mCAhBzF;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BxB", "debugId": null}}]}