"use client";

import React from "react";
import { motion, type AnimationProps } from "framer-motion";

import { cn } from "@/lib/utils";

const animationProps: AnimationProps = {
  initial: { "--x": "100%", scale: 0.8 },
  animate: { "--x": "-100%", scale: 1 },
  whileTap: { scale: 0.95 },
  transition: {
    repeat: Infinity,
    repeatType: "loop",
    repeatDelay: 1,
    type: "spring",
    stiffness: 20,
    damping: 15,
    mass: 2,
    scale: {
      type: "spring",
      stiffness: 200,
      damping: 5,
      mass: 0.5,
    },
  },
};

interface NewShinyButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  className?: string;
}

export const NewShinyButton: React.FC<NewShinyButtonProps> = ({
  children,
  className,
  ...props
}) => {
  return (
    <motion.button
      {...animationProps}
      {...props}
      className={cn(
        "relative rounded-lg font-medium backdrop-blur-xl transition-shadow duration-300 ease-in-out hover:shadow",
        "bg-[#B8956A] text-white drop-shadow-lg",
        "dark:bg-[#B8956A] dark:hover:shadow-[0_0_20px_rgba(184,149,106,0.3)]",
        // Responsive padding and sizing
        "px-4 py-2 sm:px-6 sm:py-3 md:px-8 md:py-4",
        "flex items-center justify-center text-center",
        "min-h-[44px] w-auto",
        // Ensure text stays on one line for longer content
        "whitespace-nowrap",
        className
      )}
      style={{
        "--primary": "184 149 106", // #B8956A in RGB
      } as React.CSSProperties}
    >
      <span
        className="relative block text-sm sm:text-base md:text-lg uppercase tracking-wide text-white font-medium"
        style={{
          maskImage:
            "linear-gradient(-75deg,rgb(184,149,106) calc(var(--x) + 20%),transparent calc(var(--x) + 30%),rgb(184,149,106) calc(var(--x) + 100%))",
        }}
      >
        {children}
      </span>
      <span
        style={{
          mask: "linear-gradient(rgb(0,0,0), rgb(0,0,0)) content-box,linear-gradient(rgb(0,0,0), rgb(0,0,0))",
          maskComposite: "exclude",
        }}
        className="absolute inset-0 z-10 block rounded-[inherit] bg-[linear-gradient(-75deg,rgba(184,149,106,0.1)_calc(var(--x)+20%),rgba(184,149,106,0.5)_calc(var(--x)+25%),rgba(184,149,106,0.1)_calc(var(--x)+100%))] p-px"
      ></span>
    </motion.button>
  );
};

export default NewShinyButton;
