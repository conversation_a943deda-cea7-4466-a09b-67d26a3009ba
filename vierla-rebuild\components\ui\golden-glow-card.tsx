import React from "react";
import { cn } from "@/lib/utils";
import { GoldenGlowEffect } from "./golden-glow-effect";

export const GoldenGlowCardContainer = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <div className={cn("relative h-full rounded-[1.25rem] border-[0.75px] border-border p-2 md:rounded-[1.5rem] md:p-3", className)}>
      <GoldenGlowEffect
        spread={40}
        glow={true}
        disabled={false}
        proximity={64}
        inactiveZone={0.01}
        borderWidth={3}
      />
      <div className="relative flex h-full flex-col justify-between gap-6 overflow-hidden rounded-xl border-[0.75px] bg-white/10 backdrop-blur-md p-6 shadow-xl border-white/20">
        {children}
      </div>
    </div>
  );
};
