"use client";

import React from "react";
import { cn } from "@/lib/utils";

interface GoldenGlowCardWithMarginProps {
  children: React.ReactNode;
  className?: string;
}

export const GoldenGlowCardWithMargin: React.FC<GoldenGlowCardWithMarginProps> = ({
  children,
  className,
}) => {
  return (
    <div className={cn("relative group h-full", className)}>
      {/* Golden glow effect */}
      <div className="absolute -inset-0.5 rounded-lg bg-gradient-to-r from-[#B8956A]/30 via-[#B8956A]/60 to-[#B8956A]/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm" />
      
      {/* Outer card layer */}
      <div className="relative h-full bg-white/5 backdrop-blur-sm border border-white/10 shadow-xl rounded-2xl overflow-hidden p-1">
        {/* Inner card layer with margin */}
        <div className="h-full bg-white/10 backdrop-blur-md border border-white/20 shadow-xl rounded-xl overflow-hidden">
          {children}
        </div>
      </div>
    </div>
  );
};

export default GoldenGlowCardWithMargin;
