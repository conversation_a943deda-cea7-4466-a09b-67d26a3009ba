"use client";
import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";

interface ApplicationData {
  // Personal Information
  firstName: string;
  lastName: string;
  email: string;
  phone: string;

  // Professional Information
  businessName: string;
  services: string[];
  experience: string;
  certifications: string[];

  // Location & Availability
  serviceAreas: string[];
  availability: string[];
  travelRadius: string;

  // Business Details
  insurance: boolean;
  license: string;
  portfolio: string;
  rates: string;

  // Additional Information
  motivation: string;
  references: string;
}

const serviceOptions = [
  'Hair Styling', 'Hair Cutting', 'Hair Coloring', 'Blowouts',
  'Makeup Application', 'Bridal Makeup', 'Special Event Makeup',
  'Manicures', 'Pedicures', 'Nail Art', 'Gel Polish',
  'Eyebrow Shaping', 'Eyebrow Threading', 'Eyebrow Tinting',
  'Eyelash Extensions', 'Lash Lifts', 'Lash Tinting',
  'Braiding', 'Box Braids', 'Cornrows', 'Protective Styles',
  'Loc Maintenance', 'Loc Styling', 'Retwisting',
  'Beard Trimming', 'Hot Towel Shaves', 'Men\'s Grooming'
];

const steps = [
  { id: 1, name: "Personal Information", stepWord: "Personal", fields: ["firstName", "lastName", "email", "phone"] },
  { id: 2, name: "Professional Details", stepWord: "Professional", fields: ["businessName", "services", "experience", "certifications"] },
  { id: 3, name: "Service Areas", stepWord: "Location", fields: ["serviceAreas", "availability", "travelRadius"] },
  { id: 4, name: "Business Information", stepWord: "Business", fields: ["insurance", "license", "portfolio", "rates"] },
  { id: 5, name: "Additional Details", stepWord: "Details", fields: ["motivation", "references"] },
  { id: 6, name: "Review & Submit", stepWord: "Review", fields: [] },
];

export function MultiStepForm() {
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<ApplicationData>({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    businessName: "",
    services: [],
    experience: "",
    certifications: [],
    serviceAreas: [],
    availability: [],
    travelRadius: "",
    insurance: false,
    license: "",
    portfolio: "",
    rates: "",
    motivation: "",
    references: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  const next = () => setCurrentStep((prev) => (prev < steps.length - 1 ? prev + 1 : prev));
  const prev = () => setCurrentStep((prev) => (prev > 0 ? prev - 1 : prev));

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleArrayToggle = (field: keyof ApplicationData, value: string) => {
    const currentArray = formData[field] as string[];
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value];
    updateFormData(field, newArray);
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      // Submit to the exact same API endpoint as SOURCE
      const response = await fetch('/api/professional-application', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          timestamp: new Date().toISOString()
        }),
      });

      if (response.ok) {
        setSubmitSuccess(true);
      } else {
        throw new Error('Submission failed');
      }
    } catch (error) {
      // Fallback to localStorage (same as SOURCE)
      const existingApplications = JSON.parse(localStorage.getItem('vierla-applications') || '[]');
      existingApplications.push({
        ...formData,
        timestamp: new Date().toISOString(),
        status: 'pending'
      });
      localStorage.setItem('vierla-applications', JSON.stringify(existingApplications));
      setSubmitSuccess(true);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (submitSuccess) {
    return (
      <Card className="w-full max-w-2xl mx-auto bg-white/10 backdrop-blur-md border border-white/20">
        <CardContent className="p-8 text-center">
          <h2 className="text-2xl font-bold mb-4 text-primary drop-shadow-lg">Application Submitted!</h2>
          <p className="text-white/80 mb-6 drop-shadow-sm">
            Thank you for your application. We'll review it and get back to you within 2-3 business days.
          </p>
          <Button asChild>
            <a href="/">Return to Homepage</a>
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* Progress Indicator */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          {steps.map((step, index) => (
            <div
              key={step.id}
              className={`flex items-center ${index < steps.length - 1 ? 'flex-1' : ''}`}
            >
              <div
                className={`w-auto min-w-[60px] h-8 rounded-full flex items-center justify-center text-xs font-medium px-2 ${
                  index <= currentStep
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted text-muted-foreground'
                }`}
              >
                {step.stepWord}
              </div>
              {index < steps.length - 1 && (
                <div
                  className={`flex-1 h-1 mx-2 ${
                    index < currentStep ? 'bg-primary' : 'bg-muted'
                  }`}
                />
              )}
            </div>
          ))}
        </div>
        <p className="text-center text-sm text-white drop-shadow-sm">
          Step {currentStep + 1} of {steps.length}: {steps[currentStep].name}
        </p>
      </div>

      <Card className="w-full bg-white/10 backdrop-blur-md border border-white/20">
        <CardHeader>
          <CardTitle className="text-white drop-shadow-lg">{steps[currentStep].name}</CardTitle>
          <CardDescription className="text-white/80 drop-shadow-sm">
            {currentStep === 0 && "Let's start with your basic information"}
            {currentStep === 1 && "Tell us about your professional background"}
            {currentStep === 2 && "Where do you provide services?"}
            {currentStep === 3 && "Business credentials and portfolio"}
            {currentStep === 4 && "Help us understand your motivation"}
            {currentStep === 5 && "Review your information before submitting"}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ x: 300, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: -300, opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              {/* Step 1: Personal Information */}
              {currentStep === 0 && (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="firstName" className="text-white drop-shadow-sm">First Name</Label>
                      <Input
                        id="firstName"
                        value={formData.firstName}
                        onChange={(e) => updateFormData('firstName', e.target.value)}
                        placeholder="Enter your first name"
                        className="text-white placeholder:text-[#F5F5DC] bg-white/10 border-white/20"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="lastName" className="text-white drop-shadow-sm">Last Name</Label>
                      <Input
                        id="lastName"
                        value={formData.lastName}
                        onChange={(e) => updateFormData('lastName', e.target.value)}
                        placeholder="Enter your last name"
                        className="text-white placeholder:text-[#F5F5DC] bg-white/10 border-white/20"
                        required
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email" className="text-white drop-shadow-sm">Email Address</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => updateFormData('email', e.target.value)}
                      placeholder="Enter your email address"
                      className="text-white placeholder:text-[#F5F5DC] bg-white/10 border-white/20"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone" className="text-white drop-shadow-sm">Phone Number</Label>
                    <Input
                      id="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => updateFormData('phone', e.target.value)}
                      placeholder="Enter your phone number"
                      className="text-white placeholder:text-[#F5F5DC] bg-white/10 border-white/20"
                      required
                    />
                  </div>
                </div>
              )}

              {/* Step 2: Professional Details */}
              {currentStep === 1 && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="businessName" className="text-white drop-shadow-sm">Business Name</Label>
                    <Input
                      id="businessName"
                      value={formData.businessName}
                      onChange={(e) => updateFormData('businessName', e.target.value)}
                      placeholder="Enter your business name"
                      className="text-white placeholder:text-[#F5F5DC] bg-white/10 border-white/20"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label className="text-white drop-shadow-sm">Services Offered</Label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-48 overflow-y-auto">
                      {serviceOptions.map((service) => (
                        <div key={service} className="flex items-center space-x-2">
                          <Checkbox
                            id={service}
                            checked={formData.services.includes(service)}
                            onCheckedChange={() => handleArrayToggle('services', service)}
                          />
                          <Label htmlFor={service} className="text-sm text-white drop-shadow-sm">{service}</Label>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="experience" className="text-white drop-shadow-sm">Years of Experience</Label>
                    <Input
                      id="experience"
                      value={formData.experience}
                      onChange={(e) => updateFormData('experience', e.target.value)}
                      placeholder="e.g., 5 years"
                      className="text-white placeholder:text-[#F5F5DC] bg-white/10 border-white/20"
                    />
                  </div>
                </div>
              )}

              {/* Step 3: Service Areas */}
              {currentStep === 2 && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label className="text-white drop-shadow-sm">Service Areas</Label>
                    <div className="grid grid-cols-2 gap-2">
                      {['Toronto Downtown', 'North York', 'Scarborough', 'Etobicoke', 'Mississauga', 'Brampton', 'Ottawa Downtown', 'Kanata', 'Orleans', 'Nepean'].map((area) => (
                        <div key={area} className="flex items-center space-x-2">
                          <Checkbox
                            id={area}
                            checked={formData.serviceAreas.includes(area)}
                            onCheckedChange={() => handleArrayToggle('serviceAreas', area)}
                          />
                          <Label htmlFor={area} className="text-sm text-white drop-shadow-sm">{area}</Label>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="travelRadius" className="text-white drop-shadow-sm">Travel Radius (km)</Label>
                    <Input
                      id="travelRadius"
                      value={formData.travelRadius}
                      onChange={(e) => updateFormData('travelRadius', e.target.value)}
                      placeholder="e.g., 25"
                      className="text-white placeholder:text-[#F5F5DC] bg-white/10 border-white/20"
                    />
                  </div>
                </div>
              )}

              {/* Step 4: Business Information */}
              {currentStep === 3 && (
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="insurance"
                      checked={formData.insurance}
                      onCheckedChange={(checked) => updateFormData('insurance', checked)}
                    />
                    <Label htmlFor="insurance" className="text-white drop-shadow-sm">I have professional liability insurance</Label>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="license" className="text-white drop-shadow-sm">License/Certification Numbers</Label>
                    <Input
                      id="license"
                      value={formData.license}
                      onChange={(e) => updateFormData('license', e.target.value)}
                      placeholder="Enter relevant license numbers"
                      className="text-white placeholder:text-[#F5F5DC] bg-white/10 border-white/20"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="portfolio" className="text-white drop-shadow-sm">Portfolio/Website URL</Label>
                    <Input
                      id="portfolio"
                      value={formData.portfolio}
                      onChange={(e) => updateFormData('portfolio', e.target.value)}
                      placeholder="https://your-portfolio.com"
                      className="text-white placeholder:text-[#F5F5DC] bg-white/10 border-white/20"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="rates" className="text-white drop-shadow-sm">Starting Rates</Label>
                    <Textarea
                      id="rates"
                      value={formData.rates}
                      onChange={(e) => updateFormData('rates', e.target.value)}
                      placeholder="Describe your pricing structure"
                      className="text-white placeholder:text-[#F5F5DC] bg-white/10 border-white/20"
                      rows={3}
                    />
                  </div>
                </div>
              )}

              {/* Step 5: Additional Details */}
              {currentStep === 4 && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="motivation" className="text-white drop-shadow-sm">Why do you want to join Vierla?</Label>
                    <Textarea
                      id="motivation"
                      value={formData.motivation}
                      onChange={(e) => updateFormData('motivation', e.target.value)}
                      placeholder="Tell us about your motivation and goals"
                      className="text-white placeholder:text-[#F5F5DC] bg-white/10 border-white/20"
                      rows={4}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="references" className="text-white drop-shadow-sm">References (Optional)</Label>
                    <Textarea
                      id="references"
                      value={formData.references}
                      onChange={(e) => updateFormData('references', e.target.value)}
                      placeholder="Professional references or client testimonials"
                      className="text-white placeholder:text-[#F5F5DC] bg-white/10 border-white/20"
                      rows={3}
                    />
                  </div>
                </div>
              )}

              {/* Step 6: Review & Submit */}
              {currentStep === 5 && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-white drop-shadow-lg">Review Your Application</h3>
                  <div className="space-y-2 text-sm text-white/90 drop-shadow-sm">
                    <p><strong>Name:</strong> {formData.firstName} {formData.lastName}</p>
                    <p><strong>Email:</strong> {formData.email}</p>
                    <p><strong>Phone:</strong> {formData.phone}</p>
                    <p><strong>Business:</strong> {formData.businessName || 'Not specified'}</p>
                    <p><strong>Services:</strong> {formData.services.join(', ') || 'None selected'}</p>
                    <p><strong>Experience:</strong> {formData.experience || 'Not specified'}</p>
                    <p><strong>Service Areas:</strong> {formData.serviceAreas.join(', ') || 'None selected'}</p>
                    <p><strong>Insurance:</strong> {formData.insurance ? 'Yes' : 'No'}</p>
                  </div>
                  <div className="bg-white/10 p-4 rounded-lg border border-white/20">
                    <p className="text-sm text-white/70 drop-shadow-sm">
                      By submitting this application, you agree to our Terms of Service and Privacy Policy.
                      We'll review your application and contact you within 2-3 business days.
                    </p>
                  </div>
                </div>
              )}
            </motion.div>
          </AnimatePresence>

          <div className="flex justify-between pt-4">
            <Button onClick={prev} variant="outline" disabled={currentStep === 0}>
              Back
            </Button>
            {currentStep < steps.length - 1 ? (
              <Button onClick={next}>Next</Button>
            ) : (
              <Button onClick={handleSubmit} disabled={isSubmitting}>
                {isSubmitting ? "Submitting..." : "Submit Application"}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
