"use client"
import { useState } from 'react'
import Link from "next/link"
import { BackgroundBeams } from "@/components/ui/background-beams";
import ShinyButton from "@/components/ui/shiny-button";
import { NewShinyButton } from "@/components/ui/new-shiny-button";

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
    type: 'general'
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [error, setError] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError('')

    try {
      // Submit to backend API with fallback to localStorage
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          timestamp: new Date().toISOString()
        }),
      })

      const result = await response.json()

      if (result.success) {
        setIsSubmitted(true)
        setFormData({ name: '', email: '', subject: '', message: '', type: 'general' })
      } else {
        setError(result.error || 'Something went wrong. Please try again.')
      }
    } catch (err) {
      // Fallback to localStorage
      const existingContacts = JSON.parse(localStorage.getItem('vierla-contacts') || '[]')
      existingContacts.push({
        ...formData,
        timestamp: new Date().toISOString()
      })
      localStorage.setItem('vierla-contacts', JSON.stringify(existingContacts))
      setIsSubmitted(true)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      <BackgroundBeams />

      {/* Main Content */}
      <main className="relative z-10 w-full px-4 py-20 pt-32">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-6xl font-black text-white mb-6 drop-shadow-lg">
              Contact Us
            </h1>
            <p className="text-xl text-white/90 max-w-2xl mx-auto drop-shadow-sm">
              Have questions about our services? Want to join our platform as a beauty professional? We'd love to hear from you.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div className="bg-white/10 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/20">
              <h2 className="text-2xl font-bold text-white mb-6 drop-shadow-lg">
                Send us a message
              </h2>
              
              {isSubmitted ? (
                <div className="text-center py-8">
                  <div className="inline-flex items-center justify-center w-16 h-16 rounded-full mb-4 bg-primary/20">
                    <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2 drop-shadow-lg">Message Sent!</h3>
                  <p className="text-white/80 drop-shadow-sm">Thank you for reaching out. We'll get back to you within 24 hours.</p>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-white/90 text-sm font-medium mb-2 drop-shadow-sm">Name *</label>
                      <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 rounded-xl bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent backdrop-blur-sm"
                        placeholder="Your full name"
                      />
                    </div>
                    <div>
                      <label className="block text-white/90 text-sm font-medium mb-2 drop-shadow-sm">Email *</label>
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 rounded-xl bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent backdrop-blur-sm"
                        placeholder="<EMAIL>"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2 drop-shadow-sm">Contact Type</label>
                    <select
                      name="type"
                      value={formData.type}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 rounded-xl bg-white/10 border border-white/20 text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent backdrop-blur-sm"
                    >
                      <option value="general" className="bg-gray-800">General Inquiry</option>
                      <option value="customer" className="bg-gray-800">Customer Support</option>
                      <option value="professional" className="bg-gray-800">Professional Application</option>
                      <option value="partnership" className="bg-gray-800">Partnership</option>
                      <option value="support" className="bg-gray-800">Technical Support</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2 drop-shadow-sm">Subject *</label>
                    <input
                      type="text"
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      required
                      className="w-full px-4 py-3 rounded-xl bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent backdrop-blur-sm"
                      placeholder="What's this about?"
                    />
                  </div>

                  <div>
                    <label className="block text-white/90 text-sm font-medium mb-2 drop-shadow-sm">Message *</label>
                    <textarea
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      required
                      rows={5}
                      className="w-full px-4 py-3 rounded-xl bg-white/10 border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent backdrop-blur-sm resize-none"
                      placeholder="Tell us more about your inquiry..."
                    />
                  </div>

                  {error && (
                    <div className="p-4 rounded-xl bg-red-500/20 border border-red-500/30">
                      <p className="text-red-200 text-sm drop-shadow-sm">{error}</p>
                    </div>
                  )}

                  <NewShinyButton
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full py-4 text-lg"
                  >
                    {isSubmitting ? 'Sending...' : 'Send Message'}
                  </NewShinyButton>
                </form>
              )}
            </div>

            {/* Contact Information */}
            <div className="space-y-8">
              <div className="bg-white/10 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/20">
                <h3 className="text-xl font-bold text-white mb-6 drop-shadow-lg">
                  Get in Touch
                </h3>
                
                <div className="space-y-6">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 rounded-xl flex items-center justify-center bg-white/10 border border-white/20">
                      <svg className="w-6 h-6 text-white drop-shadow-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-1 drop-shadow-lg">Email</h3>
                      <p className="text-white/80 drop-shadow-sm"><EMAIL></p>
                      <p className="text-white/60 text-sm drop-shadow-sm">We typically respond within 24 hours</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 rounded-xl flex items-center justify-center bg-white/10 border border-white/20">
                      <svg className="w-6 h-6 text-white drop-shadow-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-1 drop-shadow-lg">Location</h3>
                      <p className="text-white/80 drop-shadow-sm">Toronto & Ottawa, Ontario</p>
                      <p className="text-white/60 text-sm drop-shadow-sm">Serving the Greater Toronto and Ottawa areas</p>
                    </div>
                  </div>

                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 rounded-xl flex items-center justify-center bg-white/10 border border-white/20">
                      <svg className="w-6 h-6 text-white drop-shadow-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-1 drop-shadow-lg">Response Time</h3>
                      <p className="text-white/80 drop-shadow-sm">Within 24 hours</p>
                      <p className="text-white/60 text-sm drop-shadow-sm">Monday to Friday, 9 AM - 6 PM EST</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white/10 backdrop-blur-md rounded-3xl p-8 shadow-2xl border border-white/20">
                <h3 className="text-xl font-bold text-white mb-4 drop-shadow-lg">
                  For Beauty Professionals
                </h3>
                <p className="text-white/80 mb-4 drop-shadow-sm">
                  Interested in joining our platform? We're always looking for talented, licensed beauty professionals.
                </p>
                <Link href="/apply">
                  <ShinyButton className="w-full">
                    Apply Now
                  </ShinyButton>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
