import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import ShinyButton from "@/components/ui/shiny-button";
import { Sheet, She<PERSON><PERSON>ontent, SheetTrigger, SheetTitle } from "@/components/ui/sheet";
import { Menu } from "lucide-react";
import { HeartIcon } from "@/components/ui/heart-icon";

export function Navbar() {
  return (
    <header className="flex h-20 w-full items-center justify-between px-4 md:px-6 bg-transparent absolute top-0 left-0 z-50 border-b border-white/20">
      <Link href="/" className="flex items-center gap-3 group" prefetch={false}>
        <div className="w-10 h-10 md:w-12 md:h-12 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center shadow-lg border border-white/30 transform group-hover:scale-110 transition-all duration-300">
          <HeartIcon className="w-5 h-5 md:w-7 md:h-7 text-white" />
        </div>
        <span className="text-3xl md:text-4xl font-bold text-white drop-shadow-lg">Vierla</span>
      </Link>
      <div className="hidden items-center gap-6 text-sm font-medium md:flex">
        <nav className="flex items-center gap-6">
          <Link href="/features" className="hover:underline underline-offset-4 text-white drop-shadow-sm">Features</Link>
          <Link href="/pricing" className="hover:underline underline-offset-4 text-white drop-shadow-sm">Pricing</Link>
          <Link href="/about" className="hover:underline underline-offset-4 text-white drop-shadow-sm">About</Link>
        </nav>
        <Link href="/apply">
          <ShinyButton>Get Started</ShinyButton>
        </Link>
      </div>
      <Sheet>
        <SheetTrigger asChild>
          <Button variant="outline" size="icon" className="md:hidden border-white/30 bg-transparent hover:bg-white/10">
            <Menu className="h-6 w-6 text-white" />
            <span className="sr-only">Toggle navigation menu</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="right" className="bg-black/80 backdrop-blur-xl border-white/20">
          <SheetTitle className="sr-only">Navigation Menu</SheetTitle>
          <div className="flex flex-col space-y-4 mt-8">
            <Link href="/features" className="text-lg font-medium text-white drop-shadow-lg hover:text-primary">Features</Link>
            <Link href="/pricing" className="text-lg font-medium text-white drop-shadow-lg hover:text-primary">Pricing</Link>
            <Link href="/about" className="text-lg font-medium text-white drop-shadow-lg hover:text-primary">About</Link>
            <Link href="/contact" className="text-lg font-medium text-white drop-shadow-lg hover:text-primary">Contact</Link>
            <div className="pt-4 border-t border-white/20">
              <Link href="/apply" className="block">
                <ShinyButton className="w-full">Get Started</ShinyButton>
              </Link>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </header>
  );
}
