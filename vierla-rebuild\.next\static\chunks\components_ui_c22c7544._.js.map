{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/background-beams.tsx"], "sourcesContent": ["\"use client\";\nimport React from \"react\";\nimport { motion } from \"framer-motion\";\nimport { cn } from \"@/lib/utils\";\n\nexport const BackgroundBeams = ({ className }: { className?: string }) => {\n  return (\n    <div\n      className={cn(\n        \"absolute top-0 left-0 w-full h-full -z-10\",\n        className\n      )}\n    >\n      <div className=\"relative w-full h-full overflow-hidden\">\n        <div className=\"absolute inset-0 bg-zinc-900\"></div>\n        <div className=\"absolute h-full w-full\">\n          {/* Placeholder for beam elements */}\n          {/* This component often requires more complex SVG/div structures for the beams themselves. */}\n          {/* The following is a simplified representation. Refer to the source for the full SVG implementation. */}\n          <div className=\"absolute top-0 left-1/4 h-full w-px bg-gradient-to-b from-transparent via-purple-500 to-transparent animate-[beam_8s_linear_infinite]\"></div>\n          <div className=\"absolute top-0 left-1/2 h-full w-px bg-gradient-to-b from-transparent via-purple-500 to-transparent animate-[beam_10s_linear_infinite_2s_]\"></div>\n          <div className=\"absolute top-0 left-3/4 h-full w-px bg-gradient-to-b from-transparent via-purple-500 to-transparent animate-[beam_9s_linear_infinite_1s_]\"></div>\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKO,MAAM,kBAAkB;QAAC,EAAE,SAAS,EAA0B;IACnE,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,6CACA;kBAGF,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAI,WAAU;;sCAIb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKzB;KArBa", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/golden-glow-effect.tsx"], "sourcesContent": ["\"use client\";\n\nimport { memo, useCallback, useEffect, useRef } from \"react\";\nimport { cn } from \"@/lib/utils\";\nimport { animate } from \"motion/react\";\n\ninterface GoldenGlowEffectProps {\n  blur?: number;\n  inactiveZone?: number;\n  proximity?: number;\n  spread?: number;\n  glow?: boolean;\n  className?: string;\n  disabled?: boolean;\n  movementDuration?: number;\n  borderWidth?: number;\n}\n\nconst GoldenGlowEffect = memo(\n  ({\n    blur = 0,\n    inactiveZone = 0.7,\n    proximity = 0,\n    spread = 20,\n    glow = false,\n    className,\n    movementDuration = 2,\n    borderWidth = 1,\n    disabled = true,\n  }: GoldenGlowEffectProps) => {\n    const containerRef = useRef<HTMLDivElement>(null);\n    const lastPosition = useRef({ x: 0, y: 0 });\n    const animationFrameRef = useRef<number>(0);\n\n    const handleMove = useCallback(\n      (e?: MouseEvent | { x: number; y: number }) => {\n        if (!containerRef.current) return;\n\n        if (animationFrameRef.current) {\n          cancelAnimationFrame(animationFrameRef.current);\n        }\n\n        animationFrameRef.current = requestAnimationFrame(() => {\n          const element = containerRef.current;\n          if (!element) return;\n\n          const { left, top, width, height } = element.getBoundingClientRect();\n          const mouseX = e?.x ?? lastPosition.current.x;\n          const mouseY = e?.y ?? lastPosition.current.y;\n\n          if (e) {\n            lastPosition.current = { x: mouseX, y: mouseY };\n          }\n\n          const center = [left + width * 0.5, top + height * 0.5];\n          const distanceFromCenter = Math.hypot(\n            mouseX - center[0],\n            mouseY - center[1]\n          );\n          const inactiveRadius = 0.5 * Math.min(width, height) * inactiveZone;\n\n          if (distanceFromCenter < inactiveRadius) {\n            element.style.setProperty(\"--active\", \"0\");\n            return;\n          }\n\n          const isActive =\n            mouseX > left - proximity &&\n            mouseX < left + width + proximity &&\n            mouseY > top - proximity &&\n            mouseY < top + height + proximity;\n\n          element.style.setProperty(\"--active\", isActive ? \"1\" : \"0\");\n\n          if (!isActive) return;\n\n          const currentAngle =\n            parseFloat(element.style.getPropertyValue(\"--start\")) || 0;\n          let targetAngle =\n            (180 * Math.atan2(mouseY - center[1], mouseX - center[0])) /\n              Math.PI +\n            90;\n\n          const angleDiff = ((targetAngle - currentAngle + 180) % 360) - 180;\n          const newAngle = currentAngle + angleDiff;\n\n          animate(currentAngle, newAngle, {\n            duration: movementDuration,\n            ease: [0.16, 1, 0.3, 1],\n            onUpdate: (value) => {\n              element.style.setProperty(\"--start\", String(value));\n            },\n          });\n        });\n      },\n      [inactiveZone, proximity, movementDuration]\n    );\n\n    useEffect(() => {\n      if (disabled) return;\n\n      const handleScroll = () => handleMove();\n      const handlePointerMove = (e: PointerEvent) => handleMove(e);\n\n      window.addEventListener(\"scroll\", handleScroll, { passive: true });\n      document.body.addEventListener(\"pointermove\", handlePointerMove, {\n        passive: true,\n      });\n\n      return () => {\n        if (animationFrameRef.current) {\n          cancelAnimationFrame(animationFrameRef.current);\n        }\n        window.removeEventListener(\"scroll\", handleScroll);\n        document.body.removeEventListener(\"pointermove\", handlePointerMove);\n      };\n    }, [handleMove, disabled]);\n\n    return (\n      <>\n        <div\n          className={cn(\n            \"pointer-events-none absolute -inset-px hidden rounded-[inherit] border opacity-0 transition-opacity\",\n            glow && \"opacity-100\",\n            \"border-[#B8956A]\",\n            disabled && \"!block\"\n          )}\n        />\n        <div\n          ref={containerRef}\n          style={\n            {\n              \"--blur\": `${blur}px`,\n              \"--spread\": spread,\n              \"--start\": \"0\",\n              \"--active\": \"0\",\n              \"--glowingeffect-border-width\": `${borderWidth}px`,\n              \"--repeating-conic-gradient-times\": \"5\",\n              \"--gradient\": `radial-gradient(circle, #B8956A 10%, #B8956A00 20%),\n                radial-gradient(circle at 40% 40%, #D4AF37 5%, #D4AF3700 15%),\n                radial-gradient(circle at 60% 60%, #B8956A 10%, #B8956A00 20%), \n                radial-gradient(circle at 40% 60%, #DAA520 10%, #DAA52000 20%),\n                repeating-conic-gradient(\n                  from 236.84deg at 50% 50%,\n                  #B8956A 0%,\n                  #D4AF37 calc(25% / var(--repeating-conic-gradient-times)),\n                  #B8956A calc(50% / var(--repeating-conic-gradient-times)), \n                  #DAA520 calc(75% / var(--repeating-conic-gradient-times)),\n                  #B8956A calc(100% / var(--repeating-conic-gradient-times))\n                )`,\n            } as React.CSSProperties\n          }\n          className={cn(\n            \"pointer-events-none absolute inset-0 rounded-[inherit] opacity-100 transition-opacity\",\n            glow && \"opacity-100\",\n            blur > 0 && \"blur-[var(--blur)] \",\n            className,\n            disabled && \"!hidden\"\n          )}\n        >\n          <div\n            className={cn(\n              \"glow\",\n              \"rounded-[inherit]\",\n              'after:content-[\"\"] after:rounded-[inherit] after:absolute after:inset-[calc(-1*var(--glowingeffect-border-width))]',\n              \"after:[border:var(--glowingeffect-border-width)_solid_transparent]\",\n              \"after:[background:var(--gradient)] after:[background-attachment:fixed]\",\n              \"after:opacity-[var(--active)] after:transition-opacity after:duration-300\",\n              \"after:[mask-clip:padding-box,border-box]\",\n              \"after:[mask-composite:intersect]\",\n              \"after:[mask-image:linear-gradient(#0000,#0000),conic-gradient(from_calc((var(--start)-var(--spread))*1deg),#00000000_0deg,#fff,#00000000_calc(var(--spread)*2deg))]\"\n            )}\n          />\n        </div>\n      </>\n    );\n  }\n);\n\nGoldenGlowEffect.displayName = \"GoldenGlowEffect\";\n\nexport { GoldenGlowEffect };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAkBA,MAAM,iCAAmB,GAAA,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,UAC1B;QAAC,EACC,OAAO,CAAC,EACR,eAAe,GAAG,EAClB,YAAY,CAAC,EACb,SAAS,EAAE,EACX,OAAO,KAAK,EACZ,SAAS,EACT,mBAAmB,CAAC,EACpB,cAAc,CAAC,EACf,WAAW,IAAI,EACO;;IACtB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IACzC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAU;IAEzC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAC3B,CAAC;YACC,IAAI,CAAC,aAAa,OAAO,EAAE;YAE3B,IAAI,kBAAkB,OAAO,EAAE;gBAC7B,qBAAqB,kBAAkB,OAAO;YAChD;YAEA,kBAAkB,OAAO,GAAG;4DAAsB;oBAChD,MAAM,UAAU,aAAa,OAAO;oBACpC,IAAI,CAAC,SAAS;oBAEd,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,QAAQ,qBAAqB;wBACnD;oBAAf,MAAM,SAAS,CAAA,OAAA,cAAA,wBAAA,EAAG,CAAC,cAAJ,kBAAA,OAAQ,aAAa,OAAO,CAAC,CAAC;wBAC9B;oBAAf,MAAM,SAAS,CAAA,OAAA,cAAA,wBAAA,EAAG,CAAC,cAAJ,kBAAA,OAAQ,aAAa,OAAO,CAAC,CAAC;oBAE7C,IAAI,GAAG;wBACL,aAAa,OAAO,GAAG;4BAAE,GAAG;4BAAQ,GAAG;wBAAO;oBAChD;oBAEA,MAAM,SAAS;wBAAC,OAAO,QAAQ;wBAAK,MAAM,SAAS;qBAAI;oBACvD,MAAM,qBAAqB,KAAK,KAAK,CACnC,SAAS,MAAM,CAAC,EAAE,EAClB,SAAS,MAAM,CAAC,EAAE;oBAEpB,MAAM,iBAAiB,MAAM,KAAK,GAAG,CAAC,OAAO,UAAU;oBAEvD,IAAI,qBAAqB,gBAAgB;wBACvC,QAAQ,KAAK,CAAC,WAAW,CAAC,YAAY;wBACtC;oBACF;oBAEA,MAAM,WACJ,SAAS,OAAO,aAChB,SAAS,OAAO,QAAQ,aACxB,SAAS,MAAM,aACf,SAAS,MAAM,SAAS;oBAE1B,QAAQ,KAAK,CAAC,WAAW,CAAC,YAAY,WAAW,MAAM;oBAEvD,IAAI,CAAC,UAAU;oBAEf,MAAM,eACJ,WAAW,QAAQ,KAAK,CAAC,gBAAgB,CAAC,eAAe;oBAC3D,IAAI,cACF,AAAC,MAAM,KAAK,KAAK,CAAC,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,MAAM,CAAC,EAAE,IACtD,KAAK,EAAE,GACT;oBAEF,MAAM,YAAY,AAAC,CAAC,cAAc,eAAe,GAAG,IAAI,MAAO;oBAC/D,MAAM,WAAW,eAAe;oBAEhC,CAAA,GAAA,mLAAA,CAAA,UAAO,AAAD,EAAE,cAAc,UAAU;wBAC9B,UAAU;wBACV,MAAM;4BAAC;4BAAM;4BAAG;4BAAK;yBAAE;wBACvB,QAAQ;wEAAE,CAAC;gCACT,QAAQ,KAAK,CAAC,WAAW,CAAC,WAAW,OAAO;4BAC9C;;oBACF;gBACF;;QACF;mDACA;QAAC;QAAc;QAAW;KAAiB;IAG7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,UAAU;YAEd,MAAM;2DAAe,IAAM;;YAC3B,MAAM;gEAAoB,CAAC,IAAoB,WAAW;;YAE1D,OAAO,gBAAgB,CAAC,UAAU,cAAc;gBAAE,SAAS;YAAK;YAChE,SAAS,IAAI,CAAC,gBAAgB,CAAC,eAAe,mBAAmB;gBAC/D,SAAS;YACX;YAEA;8CAAO;oBACL,IAAI,kBAAkB,OAAO,EAAE;wBAC7B,qBAAqB,kBAAkB,OAAO;oBAChD;oBACA,OAAO,mBAAmB,CAAC,UAAU;oBACrC,SAAS,IAAI,CAAC,mBAAmB,CAAC,eAAe;gBACnD;;QACF;qCAAG;QAAC;QAAY;KAAS;IAEzB,qBACE;;0BACE,6LAAC;gBACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,uGACA,QAAQ,eACR,oBACA,YAAY;;;;;;0BAGhB,6LAAC;gBACC,KAAK;gBACL,OACE;oBACE,UAAU,AAAC,GAAO,OAAL,MAAK;oBAClB,YAAY;oBACZ,WAAW;oBACX,YAAY;oBACZ,gCAAgC,AAAC,GAAc,OAAZ,aAAY;oBAC/C,oCAAoC;oBACpC,cAAe;gBAYjB;gBAEF,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,yFACA,QAAQ,eACR,OAAO,KAAK,uBACZ,WACA,YAAY;0BAGd,cAAA,6LAAC;oBACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,QACA,qBACA,sHACA,sEACA,0EACA,6EACA,4CACA,oCACA;;;;;;;;;;;;;AAMZ;;AAGF,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/golden-glow-card-with-margin.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { cn } from \"@/lib/utils\";\n\ninterface GoldenGlowCardWithMarginProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const GoldenGlowCardWithMargin: React.FC<GoldenGlowCardWithMarginProps> = ({\n  children,\n  className,\n}) => {\n  return (\n    <div className={cn(\"relative group h-full\", className)}>\n      {/* Golden glow effect */}\n      <div className=\"absolute -inset-0.5 rounded-lg bg-gradient-to-r from-[#B8956A]/30 via-[#B8956A]/60 to-[#B8956A]/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm\" />\n      \n      {/* Outer card layer */}\n      <div className=\"relative h-full bg-white/5 backdrop-blur-sm border border-white/10 shadow-xl rounded-2xl overflow-hidden p-1\">\n        {/* Inner card layer with margin */}\n        <div className=\"h-full bg-white/10 backdrop-blur-md border border-white/20 shadow-xl rounded-xl overflow-hidden\">\n          {children}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default GoldenGlowCardWithMargin;\n"], "names": [], "mappings": ";;;;;AAGA;AAHA;;;AAUO,MAAM,2BAAoE;QAAC,EAChF,QAAQ,EACR,SAAS,EACV;IACC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;;0BAE1C,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC;gBAAI,WAAU;0BAEb,cAAA,6LAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX;KAlBa;uCAoBE", "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/new-shiny-button.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { motion, type AnimationProps } from \"framer-motion\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst animationProps: AnimationProps = {\n  initial: { \"--x\": \"100%\", scale: 0.8 },\n  animate: { \"--x\": \"-100%\", scale: 1 },\n  whileTap: { scale: 0.95 },\n  transition: {\n    repeat: Infinity,\n    repeatType: \"loop\",\n    repeatDelay: 1,\n    type: \"spring\",\n    stiffness: 20,\n    damping: 15,\n    mass: 2,\n    scale: {\n      type: \"spring\",\n      stiffness: 200,\n      damping: 5,\n      mass: 0.5,\n    },\n  },\n};\n\ninterface NewShinyButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const NewShinyButton: React.FC<NewShinyButtonProps> = ({\n  children,\n  className,\n  ...props\n}) => {\n  return (\n    <motion.button\n      {...animationProps}\n      {...props}\n      className={cn(\n        \"relative rounded-lg px-6 py-2 font-medium backdrop-blur-xl transition-shadow duration-300 ease-in-out hover:shadow\",\n        \"bg-[#B8956A] text-white drop-shadow-lg\",\n        \"dark:bg-[radial-gradient(circle_at_50%_0%,#B8956A_0%,transparent_60%)] dark:hover:shadow-[0_0_20px_rgba(184,149,106,0.3)]\",\n        className\n      )}\n      style={{\n        \"--primary\": \"184 149 106\", // #B8956A in RGB\n      } as React.CSSProperties}\n    >\n      <span\n        className=\"relative block size-full text-sm uppercase tracking-wide text-white font-medium\"\n        style={{\n          maskImage:\n            \"linear-gradient(-75deg,rgb(184,149,106) calc(var(--x) + 20%),transparent calc(var(--x) + 30%),rgb(184,149,106) calc(var(--x) + 100%))\",\n        }}\n      >\n        {children}\n      </span>\n      <span\n        style={{\n          mask: \"linear-gradient(rgb(0,0,0), rgb(0,0,0)) content-box,linear-gradient(rgb(0,0,0), rgb(0,0,0))\",\n          maskComposite: \"exclude\",\n        }}\n        className=\"absolute inset-0 z-10 block rounded-[inherit] bg-[linear-gradient(-75deg,rgba(184,149,106,0.1)_calc(var(--x)+20%),rgba(184,149,106,0.5)_calc(var(--x)+25%),rgba(184,149,106,0.1)_calc(var(--x)+100%))] p-px\"\n      ></span>\n    </motion.button>\n  );\n};\n\nexport default NewShinyButton;\n"], "names": [], "mappings": ";;;;;AAGA;AAEA;AALA;;;;AAOA,MAAM,iBAAiC;IACrC,SAAS;QAAE,OAAO;QAAQ,OAAO;IAAI;IACrC,SAAS;QAAE,OAAO;QAAS,OAAO;IAAE;IACpC,UAAU;QAAE,OAAO;IAAK;IACxB,YAAY;QACV,QAAQ;QACR,YAAY;QACZ,aAAa;QACb,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;QACN,OAAO;YACL,MAAM;YACN,WAAW;YACX,SAAS;YACT,MAAM;QACR;IACF;AACF;AAQO,MAAM,iBAAgD;QAAC,EAC5D,QAAQ,EACR,SAAS,EACT,GAAG,OACJ;IACC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACX,GAAG,cAAc;QACjB,GAAG,KAAK;QACT,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sHACA,0CACA,6HACA;QAEF,OAAO;YACL,aAAa;QACf;;0BAEA,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,WACE;gBACJ;0BAEC;;;;;;0BAEH,6LAAC;gBACC,OAAO;oBACL,MAAM;oBACN,eAAe;gBACjB;gBACA,WAAU;;;;;;;;;;;;AAIlB;KArCa;uCAuCE", "debugId": null}}, {"offset": {"line": 392, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/word-pull-up.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion, Variants } from \"framer-motion\";\n\nimport { cn } from \"@/lib/utils\";\n\ninterface WordPullUpProps {\n  words: string;\n  delayMultiple?: number;\n  wrapperFramerProps?: Variants;\n  framerProps?: Variants;\n  className?: string;\n}\n\nfunction WordPullUp({\n  words,\n  wrapperFramerProps = {\n    hidden: { opacity: 0 },\n    show: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2,\n      },\n    },\n  },\n  framerProps = {\n    hidden: { y: 20, opacity: 0 },\n    show: { y: 0, opacity: 1 },\n  },\n  className,\n}: WordPullUpProps) {\n  return (\n    <motion.h1\n      variants={wrapperFramerProps}\n      initial=\"hidden\"\n      animate=\"show\"\n      className={cn(\n        \"font-display text-center text-4xl font-bold leading-[5rem] tracking-[-0.02em] drop-shadow-sm text-white\",\n        className,\n      )}\n    >\n      {words.split(\" \").map((word, i) => (\n        <motion.span\n          key={i}\n          variants={framerProps}\n          style={{ display: \"inline-block\", paddingRight: \"8px\" }}\n        >\n          {word === \"\" ? <span>&nbsp;</span> : word}\n        </motion.span>\n      ))}\n    </motion.h1>\n  );\n}\n\nexport { WordPullUp };\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;AAcA,SAAS,WAAW,KAgBF;QAhBE,EAClB,KAAK,EACL,qBAAqB;QACnB,QAAQ;YAAE,SAAS;QAAE;QACrB,MAAM;YACJ,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF,CAAC,EACD,cAAc;QACZ,QAAQ;YAAE,GAAG;YAAI,SAAS;QAAE;QAC5B,MAAM;YAAE,GAAG;YAAG,SAAS;QAAE;IAC3B,CAAC,EACD,SAAS,EACO,GAhBE;IAiBlB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;QACR,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,2GACA;kBAGD,MAAM,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,MAAM,kBAC3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;gBAEV,UAAU;gBACV,OAAO;oBAAE,SAAS;oBAAgB,cAAc;gBAAM;0BAErD,SAAS,mBAAK,6LAAC;8BAAK;;;;;2BAAgB;eAJhC;;;;;;;;;;AASf;KAtCS", "debugId": null}}, {"offset": {"line": 465, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/marquee-effect.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useRef } from \"react\";\nimport {\n  motion,\n  useScroll,\n  useSpring,\n  useTransform,\n  useMotionValue,\n  useVelocity,\n  useAnimationFrame,\n} from \"framer-motion\";\nimport { wrap } from \"@motionone/utils\";\nimport { cn } from \"@/lib/utils\";\n\ntype MarqueeAnimationProps = {\n  children: string;\n  className?: string;\n  direction?: \"left\" | \"right\";\n  baseVelocity: number;\n};\n\nfunction MarqueeAnimation({\n  children,\n  className,\n  direction = \"left\",\n  baseVelocity = 10,\n}: MarqueeAnimationProps) {\n  const baseX = useMotionValue(0);\n  const { scrollY } = useScroll();\n  const scrollVelocity = useVelocity(scrollY);\n  const smoothVelocity = useSpring(scrollVelocity, {\n    damping: 50,\n    stiffness: 400,\n  });\n  const velocityFactor = useTransform(smoothVelocity, [0, 1000], [0, 0], {\n    clamp: false,\n  });\n\n  const x = useTransform(baseX, (v) => `${wrap(-20, -45, v)}%`);\n\n  const directionFactor = useRef<number>(1);\n  useAnimationFrame((t, delta) => {\n    let moveBy = directionFactor.current * baseVelocity * (delta / 1000);\n\n    if (direction == \"left\") {\n      directionFactor.current = 1;\n    } else if (direction == \"right\") {\n      directionFactor.current = -1;\n    }\n\n    moveBy += directionFactor.current * moveBy * velocityFactor.get();\n\n    baseX.set(baseX.get() + moveBy);\n  });\n\n  return (\n    <div className=\"overflow-hidden max-w-[100vw] text-nowrap flex-nowrap flex relative\">\n      <motion.div\n        className={cn(\n          \"font-bold uppercase text-5xl flex flex-nowrap text-nowrap *:block *:me-10 text-white drop-shadow-lg\",\n          className\n        )}\n        style={{ x }}\n      >\n        <span>{children}</span>\n        <span>{children}</span>\n        <span>{children}</span>\n        <span>{children}</span>\n      </motion.div>\n    </div>\n  );\n}\n\nexport { MarqueeAnimation };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;;;AAbA;;;;;AAsBA,SAAS,iBAAiB,KAKF;QALE,EACxB,QAAQ,EACR,SAAS,EACT,YAAY,MAAM,EAClB,eAAe,EAAE,EACK,GALE;;IAMxB,MAAM,QAAQ,CAAA,GAAA,qLAAA,CAAA,iBAAc,AAAD,EAAE;IAC7B,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD;IAC5B,MAAM,iBAAiB,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;IACnC,MAAM,iBAAiB,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB;QAC/C,SAAS;QACT,WAAW;IACb;IACA,MAAM,iBAAiB,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB;QAAC;QAAG;KAAK,EAAE;QAAC;QAAG;KAAE,EAAE;QACrE,OAAO;IACT;IAEA,MAAM,IAAI,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE;4CAAO,CAAC,IAAM,AAAC,GAAoB,OAAlB,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,CAAC,IAAI,CAAC,IAAI,IAAG;;IAE1D,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAU;IACvC,CAAA,GAAA,wLAAA,CAAA,oBAAiB,AAAD;8CAAE,CAAC,GAAG;YACpB,IAAI,SAAS,gBAAgB,OAAO,GAAG,eAAe,CAAC,QAAQ,IAAI;YAEnE,IAAI,aAAa,QAAQ;gBACvB,gBAAgB,OAAO,GAAG;YAC5B,OAAO,IAAI,aAAa,SAAS;gBAC/B,gBAAgB,OAAO,GAAG,CAAC;YAC7B;YAEA,UAAU,gBAAgB,OAAO,GAAG,SAAS,eAAe,GAAG;YAE/D,MAAM,GAAG,CAAC,MAAM,GAAG,KAAK;QAC1B;;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,uGACA;YAEF,OAAO;gBAAE;YAAE;;8BAEX,6LAAC;8BAAM;;;;;;8BACP,6LAAC;8BAAM;;;;;;8BACP,6LAAC;8BAAM;;;;;;8BACP,6LAAC;8BAAM;;;;;;;;;;;;;;;;;AAIf;GAlDS;;QAMO,qLAAA,CAAA,iBAAc;QACR,4KAAA,CAAA,YAAS;QACN,8KAAA,CAAA,cAAW;QACX,4KAAA,CAAA,YAAS;QAIT,+KAAA,CAAA,eAAY;QAIzB,+KAAA,CAAA,eAAY;QAGtB,wLAAA,CAAA,oBAAiB;;;KApBV", "debugId": null}}]}