import { BackgroundBeams } from "@/components/ui/background-beams";
import { GoldenGlowingCardContainer } from "@/components/ui/golden-glowing-card-container";
import { NewShinyButton } from "@/components/ui/new-shiny-button";
import { WordPullUp } from "@/components/ui/word-pull-up";
import { MarqueeAnimation } from "@/components/ui/marquee-effect";
import { LayoutTemplate, Search, Calendar, Sparkles } from "lucide-react";
import Link from "next/link";

export default function Home() {
  return (
    <div className="min-h-screen relative overflow-hidden">
      <BackgroundBeams />

      {/* Hero Section */}
      <section className="relative z-10 w-full px-4 py-20 pt-32">
        <div className="text-center max-w-6xl mx-auto">
          {/* Word Pull Up for main heading */}
          <div className="mb-8">
            <WordPullUp
              words="Self-Care, Simplified"
              className="text-6xl md:text-8xl font-black leading-none text-white drop-shadow-lg"
              delay={0}
            />
          </div>

          {/* Word Pull Up for description */}
          <div className="mb-8">
            <WordPullUp
              words="The ultimate marketplace connecting you with top beauty professionals, and the all-in-one tool for providers to manage and grow their business."
              className="text-xl md:text-2xl text-white/90 leading-relaxed font-light max-w-4xl mx-auto drop-shadow-sm"
              delay={1.5}
            />
          </div>

          {/* Marquee effect for launching soon - moved below description with larger text */}
          {/* <div className="mb-12 overflow-hidden">
            <MarqueeAnimation
              direction="left"
              baseVelocity={-1}
              className="text-white/80 text-3xl md:text-4xl font-bold drop-shadow-lg bg-transparent py-4"
            >
              Launching Soon in Toronto & Ottawa - Ontario's Premier Beauty Marketplace
            </MarqueeAnimation>
          </div> */}
          <div className="mb-12 overflow-hidden">
            <WordPullUp
              words="Launching Soon in Toronto & Ottawa - Ontario's Premier Beauty Marketplace"
              className="text-white/80 text-3xl md:text-4xl font-bold drop-shadow-lg bg-transparent py-4"
              delay={4.5}
            />
          </div>

          {/* Dual Call-to-Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
            {/* Commented out Find Your Perfect Stylist button */}
            {/* <Link
              href="/customer-app"
              className="group flex items-center px-8 py-4 rounded-full font-medium transition-all duration-300 text-lg hover:scale-105 min-w-[280px] justify-center bg-primary text-primary-foreground"
            >
              <Search className="mr-3 w-6 h-6" />
              Find Your Perfect Stylist
              <Sparkles className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
            </Link> */}

            <Link href="/provider-app">
              <NewShinyButton className="group">
                <LayoutTemplate className="mr-3 w-5 h-5 md:w-6 md:h-6" />
                Grow Your Business
                <svg className="ml-2 w-4 h-4 md:w-5 md:h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </NewShinyButton>
            </Link>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="relative z-10 py-20 border-t border-white/20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-5xl font-black text-white mb-6 drop-shadow-lg">
              How It Works
            </h2>
            <p className="text-xl text-white/90 max-w-3xl mx-auto drop-shadow-sm">
              Getting beautiful has never been this simple
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8 max-w-6xl mx-auto">
            {/* 1. Discover */}
            <GoldenGlowingCardContainer>
              <div className="text-center">
                <div className="w-16 h-16 md:w-20 md:h-20 mx-auto mb-4 md:mb-6 rounded-full flex items-center justify-center bg-white/20 border-2 border-white/30">
                  <Search className="w-8 h-8 md:w-10 md:h-10 text-white" />
                </div>
                <h3 className="text-lg md:text-2xl font-bold text-white mb-2 md:mb-4 drop-shadow-lg">1. Discover</h3>
                <p className="text-white/80 leading-relaxed text-sm md:text-base drop-shadow-md">
                  Explore our curated network of top-rated beauty professionals in your area.
                </p>
              </div>
            </GoldenGlowingCardContainer>

            {/* 2. Book */}
            <GoldenGlowingCardContainer>
              <div className="text-center">
                <div className="w-16 h-16 md:w-20 md:h-20 mx-auto mb-4 md:mb-6 rounded-full flex items-center justify-center bg-white/20 border-2 border-white/30">
                  <Calendar className="w-8 h-8 md:w-10 md:h-10 text-white" />
                </div>
                <h3 className="text-lg md:text-2xl font-bold text-white mb-2 md:mb-4 drop-shadow-lg">2. Book</h3>
                <p className="text-white/80 leading-relaxed text-sm md:text-base drop-shadow-md">
                  Select your service, choose a time that works for you, and pay securely online.
                </p>
              </div>
            </GoldenGlowingCardContainer>

            {/* 3. Pay */}
            <GoldenGlowingCardContainer>
              <div className="text-center">
                <div className="w-16 h-16 md:w-20 md:h-20 mx-auto mb-4 md:mb-6 rounded-full flex items-center justify-center bg-white/20 border-2 border-white/30">
                  <svg className="w-8 h-8 md:w-10 md:h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                  </svg>
                </div>
                <h3 className="text-lg md:text-2xl font-bold text-white mb-2 md:mb-4 drop-shadow-lg">3. Pay</h3>
                <p className="text-white/80 leading-relaxed text-sm md:text-base drop-shadow-md">
                  Secure payment processing with multiple payment options for your convenience.
                </p>
              </div>
            </GoldenGlowingCardContainer>

            {/* 4. Relax */}
            <GoldenGlowingCardContainer>
              <div className="text-center">
                <div className="w-16 h-16 md:w-20 md:h-20 mx-auto mb-4 md:mb-6 rounded-full flex items-center justify-center bg-white/20 border-2 border-white/30">
                  <Sparkles className="w-8 h-8 md:w-10 md:h-10 text-white" />
                </div>
                <h3 className="text-lg md:text-2xl font-bold text-white mb-2 md:mb-4 drop-shadow-lg">4. Relax</h3>
                <p className="text-white/80 leading-relaxed text-sm md:text-base drop-shadow-md">
                  Your vetted professional comes to you, ready to provide an exceptional service.
                </p>
              </div>
            </GoldenGlowingCardContainer>
          </div>
        </div>
      </section>

      {/* Featured Services Section */}
      <section className="relative z-10 py-20 border-t border-white/20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-5xl font-black text-white mb-6 drop-shadow-lg">Featured Services</h2>
            <p className="text-xl text-white/90 max-w-4xl mx-auto drop-shadow-sm">
              Discover our curated selection of premium beauty services. Choose to have professionals come to you, or visit their studios.
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 max-w-7xl mx-auto">
            {[
              {
                title: "Barbers",
                services: ["• Classic cuts", "• Beard trims", "• Hot towel shaves", "• Hair styling"],
              },
              {
                title: "Makeup",
                services: ["• Event makeup", "• Bridal looks", "• Fashion makeup", "• Everyday glam"],
              },
              {
                title: "Salons",
                services: ["• Hair cuts & color", "• Blowouts", "• Treatments", "• Full styling"],
              },
              {
                title: "Locs",
                services: ["• Loc maintenance", "• Retwisting", "• Loc styling", "• Loc repair"],
              },
              {
                title: "Braids",
                services: ["• Box braids", "• Cornrows", "• French braids", "• Protective styles"],
                mobileHidden: true,
              },
              {
                title: "Nails",
                services: ["• Manicures", "• Pedicures", "• Nail art", "• Gel polish"],
                mobileHidden: true,
              },
              {
                title: "Skincare",
                services: ["• Facials", "• Chemical peels", "• Microdermabrasion", "• Anti-aging"],
                mobileHidden: true,
              },
              {
                title: "Massage",
                services: ["• Relaxation", "• Deep tissue", "• Hot stone", "• Aromatherapy"],
                mobileHidden: true,
              },
              {
                title: "Brows",
                services: ["• Eyebrow shaping", "• Threading", "• Tinting", "• Microblading"],
                mobileHidden: true,
              },
              {
                title: "Henna",
                services: ["• Traditional henna", "• Bridal designs", "• Body art", "• Custom patterns"],
                mobileHidden: true,
              },
              {
                title: "Waxing",
                services: ["• Full body wax", "• Brazilian wax", "• Facial wax", "• Leg wax"],
                mobileHidden: true,
              },
              {
                title: "Laser Hair Removal",
                services: ["• Full body laser", "• Facial laser", "• Bikini laser", "• Underarm laser"],
                mobileHidden: true,
              },
            ].map((service, index) => (
              <div key={index} className={service.mobileHidden ? "hidden md:block" : ""}>
                <GoldenGlowingCardContainer>
                  <div className="text-center h-full flex flex-col justify-between">
                    <div>
                      <h3 className="text-xl font-bold text-white mb-4 drop-shadow-lg">
                        {service.title}
                      </h3>
                      <div className="text-white/80 text-sm leading-relaxed text-left drop-shadow-sm">
                        {service.services.map((serviceItem, idx) => (
                          <div key={idx} className="mb-1">
                            {serviceItem}
                          </div>
                        ))}
                      </div>
                    </div>
                    <div className="mt-4">
                      <span className="text-xs px-3 py-1 rounded-full bg-primary/10 text-primary drop-shadow-sm">
                        Coming Soon
                      </span>
                    </div>
                  </div>
                </GoldenGlowingCardContainer>
              </div>
            ))}
          </div>
        </div>
      </section>


    </div>
  );
}
