{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_e531dabc-module__QGiZLq__className\",\n  \"variable\": \"geist_e531dabc-module__QGiZLq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22variable%22:%22--font-geist-sans%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_mono_68a01160-module__YLcDdW__className\",\n  \"variable\": \"geist_mono_68a01160-module__YLcDdW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22variable%22:%22--font-geist-mono%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,0JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,0JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,0JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,qMAAA,CAAA,aAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/shiny-button.tsx"], "sourcesContent": ["import React, { CSSProperties } from \"react\";\nimport { cn } from \"@/lib/utils\";\n\nexport interface ShimmerButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  shimmerColor?: string;\n  shimmerSize?: string;\n  borderRadius?: string;\n  shimmerDuration?: string;\n  background?: string;\n  className?: string;\n  children?: React.ReactNode;\n  asChild?: boolean;\n}\n\nconst ShinyButton = React.forwardRef<HTMLButtonElement, ShimmerButtonProps>(\n  (\n    {\n      shimmerColor = \"#ffffff\",\n      shimmerSize = \"0.05em\",\n      shimmerDuration = \"3s\",\n      borderRadius = \"100px\",\n      background = \"#B8956A\", // Use gold color instead of transparent\n      className,\n      children,\n      asChild,\n      ...props\n    },\n    ref,\n  ) => {\n    if (asChild) {\n      return (\n        <div\n          style={\n            {\n              \"--spread\": \"90deg\",\n              \"--shimmer-color\": shimmerColor,\n              \"--radius\": borderRadius,\n              \"--speed\": shimmerDuration,\n              \"--cut\": shimmerSize,\n              \"--bg\": background,\n            } as CSSProperties\n          }\n          className={cn(\n            \"group relative z-0 flex cursor-pointer items-center justify-center overflow-hidden whitespace-nowrap p-[2px] [border-radius:var(--radius)]\",\n            \"transform-gpu transition-transform duration-300 ease-in-out active:translate-y-px\",\n            \"min-h-[44px]\", // Ensure minimum height for proper shimmer effect\n            className,\n          )}\n        >\n          {/* shimmer background layer */}\n          <div\n            className={cn(\n              \"absolute inset-0 overflow-visible [container-type:size] rounded-[inherit]\",\n            )}\n          >\n            {/* shimmer effect */}\n            <div className=\"absolute inset-0 h-[100cqh] animate-shimmer-slide [aspect-ratio:1] [border-radius:0] [mask:none]\">\n              <div className=\"animate-spin-around absolute -inset-full w-auto rotate-0 [background:conic-gradient(from_calc(270deg-(var(--spread)*0.5)),transparent_0,var(--shimmer-color)_var(--spread),transparent_var(--spread))] [translate:0_0]\" />\n            </div>\n          </div>\n\n          {/* top layer with #B8956A background */}\n          <div\n            className={cn(\n              \"relative z-10 flex items-center justify-center px-6 py-3 text-white rounded-[calc(var(--radius)-2px)]\",\n              \"shadow-[inset_0_-8px_10px_#ffffff1f]\",\n              \"transform-gpu transition-all duration-300 ease-in-out\",\n              \"group-hover:shadow-[inset_0_-6px_10px_#ffffff3f]\",\n              \"group-active:shadow-[inset_0_-10px_10px_#ffffff3f]\",\n            )}\n            style={{ backgroundColor: '#B8956A' }}\n          >\n            {children}\n          </div>\n        </div>\n      );\n    }\n\n    return (\n      <button\n        style={\n          {\n            \"--spread\": \"90deg\",\n            \"--shimmer-color\": shimmerColor,\n            \"--radius\": borderRadius,\n            \"--speed\": shimmerDuration,\n            \"--cut\": shimmerSize,\n            \"--bg\": background,\n          } as CSSProperties\n        }\n        className={cn(\n          \"group relative z-0 flex cursor-pointer items-center justify-center overflow-hidden p-[2px] [border-radius:var(--radius)]\",\n          \"transform-gpu transition-transform duration-300 ease-in-out active:translate-y-px\",\n          \"min-h-[44px]\", // Ensure minimum height for proper shimmer effect\n          // Remove whitespace-nowrap to allow text wrapping for longer content\n          className,\n        )}\n        ref={ref}\n        {...props}\n      >\n        {/* shimmer background layer */}\n        <div\n          className={cn(\n            \"absolute inset-0 overflow-visible [container-type:size] rounded-[inherit]\",\n          )}\n        >\n          {/* shimmer effect */}\n          <div className=\"absolute inset-0 h-[100cqh] animate-shimmer-slide [aspect-ratio:1] [border-radius:0] [mask:none]\">\n            <div className=\"animate-spin-around absolute -inset-full w-auto rotate-0 [background:conic-gradient(from_calc(270deg-(var(--spread)*0.5)),transparent_0,var(--shimmer-color)_var(--spread),transparent_var(--spread))] [translate:0_0]\" />\n          </div>\n        </div>\n\n        {/* top layer with #B8956A background */}\n        <div\n          className={cn(\n            \"relative z-10 flex items-center justify-center text-white rounded-[calc(var(--radius)-2px)] w-full h-full\",\n            \"shadow-[inset_0_-8px_10px_#ffffff1f]\",\n            \"transform-gpu transition-all duration-300 ease-in-out\",\n            \"group-hover:shadow-[inset_0_-6px_10px_#ffffff3f]\",\n            \"group-active:shadow-[inset_0_-10px_10px_#ffffff3f]\",\n            // Default padding that can be overridden\n            \"px-6 py-3\",\n          )}\n          style={{ backgroundColor: '#B8956A' }}\n        >\n          {children}\n        </div>\n      </button>\n    );\n  },\n);\n\nShinyButton.displayName = \"ShinyButton\";\n\nexport default ShinyButton;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAcA,MAAM,4BAAc,qMAAA,CAAA,UAAK,CAAC,UAAU,CAClC,CACE,EACE,eAAe,SAAS,EACxB,cAAc,QAAQ,EACtB,kBAAkB,IAAI,EACtB,eAAe,OAAO,EACtB,aAAa,SAAS,EACtB,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OACJ,EACD;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YACC,OACE;gBACE,YAAY;gBACZ,mBAAmB;gBACnB,YAAY;gBACZ,WAAW;gBACX,SAAS;gBACT,QAAQ;YACV;YAEF,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8IACA,qFACA,gBACA;;8BAIF,8OAAC;oBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV;8BAIF,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;8BAKnB,8OAAC;oBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yGACA,wCACA,yDACA,oDACA;oBAEF,OAAO;wBAAE,iBAAiB;oBAAU;8BAEnC;;;;;;;;;;;;IAIT;IAEA,qBACE,8OAAC;QACC,OACE;YACE,YAAY;YACZ,mBAAmB;YACnB,YAAY;YACZ,WAAW;YACX,SAAS;YACT,QAAQ;QACV;QAEF,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,4HACA,qFACA,gBACA,qEAAqE;QACrE;QAEF,KAAK;QACJ,GAAG,KAAK;;0BAGT,8OAAC;gBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV;0BAIF,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;0BAKnB,8OAAC;gBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6GACA,wCACA,yDACA,oDACA,sDACA,yCAAyC;gBACzC;gBAEF,OAAO;oBAAE,iBAAiB;gBAAU;0BAEnC;;;;;;;;;;;;AAIT;AAGF,YAAY,WAAW,GAAG;uCAEX", "debugId": null}}, {"offset": {"line": 252, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/sheet.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Sheet = registerClientReference(\n    function() { throw new Error(\"Attempted to call Sheet() from the server but Sheet is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sheet.tsx <module evaluation>\",\n    \"Sheet\",\n);\nexport const SheetClose = registerClientReference(\n    function() { throw new Error(\"Attempted to call SheetClose() from the server but SheetClose is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sheet.tsx <module evaluation>\",\n    \"SheetClose\",\n);\nexport const SheetContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call Sheet<PERSON>ontent() from the server but SheetContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sheet.tsx <module evaluation>\",\n    \"SheetContent\",\n);\nexport const SheetDescription = registerClientReference(\n    function() { throw new Error(\"Attempted to call SheetDescription() from the server but SheetDescription is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sheet.tsx <module evaluation>\",\n    \"SheetDescription\",\n);\nexport const SheetFooter = registerClientReference(\n    function() { throw new Error(\"Attempted to call SheetFooter() from the server but SheetFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sheet.tsx <module evaluation>\",\n    \"SheetFooter\",\n);\nexport const SheetHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call SheetHeader() from the server but SheetHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sheet.tsx <module evaluation>\",\n    \"SheetHeader\",\n);\nexport const SheetOverlay = registerClientReference(\n    function() { throw new Error(\"Attempted to call SheetOverlay() from the server but SheetOverlay is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sheet.tsx <module evaluation>\",\n    \"SheetOverlay\",\n);\nexport const SheetPortal = registerClientReference(\n    function() { throw new Error(\"Attempted to call SheetPortal() from the server but SheetPortal is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sheet.tsx <module evaluation>\",\n    \"SheetPortal\",\n);\nexport const SheetTitle = registerClientReference(\n    function() { throw new Error(\"Attempted to call SheetTitle() from the server but SheetTitle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sheet.tsx <module evaluation>\",\n    \"SheetTitle\",\n);\nexport const SheetTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call SheetTrigger() from the server but SheetTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sheet.tsx <module evaluation>\",\n    \"SheetTrigger\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;AACO,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,yDACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,yDACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,yDACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,yDACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,yDACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,yDACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,yDACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,yDACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,yDACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,yDACA", "debugId": null}}, {"offset": {"line": 300, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/sheet.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Sheet = registerClientReference(\n    function() { throw new Error(\"Attempted to call Sheet() from the server but Sheet is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sheet.tsx\",\n    \"Sheet\",\n);\nexport const SheetClose = registerClientReference(\n    function() { throw new Error(\"Attempted to call SheetClose() from the server but SheetClose is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sheet.tsx\",\n    \"SheetClose\",\n);\nexport const SheetContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call SheetContent() from the server but Sheet<PERSON>ontent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sheet.tsx\",\n    \"SheetContent\",\n);\nexport const SheetDescription = registerClientReference(\n    function() { throw new Error(\"Attempted to call SheetDescription() from the server but SheetDescription is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sheet.tsx\",\n    \"SheetDescription\",\n);\nexport const SheetFooter = registerClientReference(\n    function() { throw new Error(\"Attempted to call SheetFooter() from the server but SheetFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sheet.tsx\",\n    \"SheetFooter\",\n);\nexport const SheetHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call SheetHeader() from the server but SheetHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sheet.tsx\",\n    \"SheetHeader\",\n);\nexport const SheetOverlay = registerClientReference(\n    function() { throw new Error(\"Attempted to call SheetOverlay() from the server but SheetOverlay is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sheet.tsx\",\n    \"SheetOverlay\",\n);\nexport const SheetPortal = registerClientReference(\n    function() { throw new Error(\"Attempted to call SheetPortal() from the server but SheetPortal is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sheet.tsx\",\n    \"SheetPortal\",\n);\nexport const SheetTitle = registerClientReference(\n    function() { throw new Error(\"Attempted to call SheetTitle() from the server but SheetTitle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sheet.tsx\",\n    \"SheetTitle\",\n);\nexport const SheetTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call SheetTrigger() from the server but SheetTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/sheet.tsx\",\n    \"SheetTrigger\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;AACO,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,qCACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,qCACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,qCACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,qCACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,qCACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,qCACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,qCACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,qCACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,qCACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,qCACA", "debugId": null}}, {"offset": {"line": 348, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 356, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/heart-icon.tsx"], "sourcesContent": ["import React from 'react'\n\ninterface HeartIconProps {\n  className?: string\n  style?: React.CSSProperties\n}\n\nexport const HeartIcon: React.FC<HeartIconProps> = ({ className = \"w-6 h-6\", style }) => {\n  return (\n    <svg\n      className={className}\n      style={style}\n      viewBox=\"0 0 24 24\"\n      fill=\"currentColor\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n    >\n      <path d=\"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\"/>\n    </svg>\n  )\n}\n\nexport default HeartIcon\n"], "names": [], "mappings": ";;;;;;AAOO,MAAM,YAAsC,CAAC,EAAE,YAAY,SAAS,EAAE,KAAK,EAAE;IAClF,qBACE,8OAAC;QACC,WAAW;QACX,OAAO;QACP,SAAQ;QACR,MAAK;QACL,OAAM;kBAEN,cAAA,8OAAC;YAAK,GAAE;;;;;;;;;;;AAGd;uCAEe", "debugId": null}}, {"offset": {"line": 387, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/marketing/navbar.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport ShinyButton from \"@/components/ui/shiny-button\";\nimport { Sheet, She<PERSON><PERSON>ontent, SheetTrigger, SheetTitle } from \"@/components/ui/sheet\";\nimport { Menu } from \"lucide-react\";\nimport { HeartIcon } from \"@/components/ui/heart-icon\";\n\nexport function Navbar() {\n  return (\n    <header className=\"flex h-20 w-full items-center justify-between px-4 md:px-6 bg-transparent absolute top-0 left-0 z-50 border-b border-white/20\">\n      <Link href=\"/\" className=\"flex items-center gap-3 group\" prefetch={false}>\n        <div className=\"w-10 h-10 md:w-12 md:h-12 bg-white/20 backdrop-blur-md rounded-2xl flex items-center justify-center shadow-lg border border-white/30 transform group-hover:scale-110 transition-all duration-300\">\n          <HeartIcon className=\"w-5 h-5 md:w-7 md:h-7 text-white\" />\n        </div>\n        <span className=\"text-3xl md:text-4xl font-bold text-white drop-shadow-lg\">Vierla</span>\n      </Link>\n      <div className=\"hidden items-center gap-6 text-sm font-medium md:flex\">\n        <nav className=\"flex items-center gap-6\">\n          <Link href=\"/features\" className=\"hover:underline underline-offset-4 text-white drop-shadow-sm\">Features</Link>\n          <Link href=\"/pricing\" className=\"hover:underline underline-offset-4 text-white drop-shadow-sm\">Pricing</Link>\n          <Link href=\"/about\" className=\"hover:underline underline-offset-4 text-white drop-shadow-sm\">About</Link>\n        </nav>\n        <Link href=\"/apply\">\n          <ShinyButton>Get Started</ShinyButton>\n        </Link>\n      </div>\n      <Sheet>\n        <SheetTrigger asChild>\n          <Button variant=\"outline\" size=\"icon\" className=\"md:hidden border-white/30 bg-transparent hover:bg-white/10\">\n            <Menu className=\"h-6 w-6 text-white\" />\n            <span className=\"sr-only\">Toggle navigation menu</span>\n          </Button>\n        </SheetTrigger>\n        <SheetContent side=\"right\" className=\"bg-black/80 backdrop-blur-xl border-white/20\">\n          <SheetTitle className=\"sr-only\">Navigation Menu</SheetTitle>\n          <div className=\"flex flex-col space-y-4 mt-8\">\n            <Link href=\"/features\" className=\"text-lg font-medium text-white drop-shadow-lg hover:text-primary\">Features</Link>\n            <Link href=\"/pricing\" className=\"text-lg font-medium text-white drop-shadow-lg hover:text-primary\">Pricing</Link>\n            <Link href=\"/about\" className=\"text-lg font-medium text-white drop-shadow-lg hover:text-primary\">About</Link>\n            <Link href=\"/contact\" className=\"text-lg font-medium text-white drop-shadow-lg hover:text-primary\">Contact</Link>\n            <div className=\"pt-4 border-t border-white/20\">\n              <Link href=\"/apply\" className=\"block\">\n                <ShinyButton className=\"w-full\">Get Started</ShinyButton>\n              </Link>\n            </div>\n          </div>\n        </SheetContent>\n      </Sheet>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;;0BAChB,8OAAC,4JAAA,CAAA,UAAI;gBAAC,MAAK;gBAAI,WAAU;gBAAgC,UAAU;;kCACjE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kIAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;kCAEvB,8OAAC;wBAAK,WAAU;kCAA2D;;;;;;;;;;;;0BAE7E,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAY,WAAU;0CAA+D;;;;;;0CAChG,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAW,WAAU;0CAA+D;;;;;;0CAC/F,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAS,WAAU;0CAA+D;;;;;;;;;;;;kCAE/F,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,8OAAC,oIAAA,CAAA,UAAW;sCAAC;;;;;;;;;;;;;;;;;0BAGjB,8OAAC,0HAAA,CAAA,QAAK;;kCACJ,8OAAC,0HAAA,CAAA,eAAY;wBAAC,OAAO;kCACnB,cAAA,8OAAC,2HAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,MAAK;4BAAO,WAAU;;8CAC9C,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;;;;;;kCAG9B,8OAAC,0HAAA,CAAA,eAAY;wBAAC,MAAK;wBAAQ,WAAU;;0CACnC,8OAAC,0HAAA,CAAA,aAAU;gCAAC,WAAU;0CAAU;;;;;;0CAChC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAY,WAAU;kDAAmE;;;;;;kDACpG,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAmE;;;;;;kDACnG,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAmE;;;;;;kDACjG,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAmE;;;;;;kDACnG,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAC5B,cAAA,8OAAC,oIAAA,CAAA,UAAW;gDAAC,WAAU;0DAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD", "debugId": null}}, {"offset": {"line": 638, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/marketing/footer.tsx"], "sourcesContent": ["import React from \"react\";\nimport Link from \"next/link\";\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-background border-t py-12\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8\">\n          <div>\n            <h2 className=\"text-xl font-bold mb-4 text-white drop-shadow-lg\">Vierla</h2>\n            <p className=\"text-white/80 text-sm drop-shadow-sm\">\n              The AI-Powered Platform for Modern Business.\n            </p>\n          </div>\n          <div>\n            <h3 className=\"font-semibold mb-4 text-white drop-shadow-lg\">Product</h3>\n            <ul className=\"space-y-2 text-sm\">\n              <li><Link href=\"/features\" className=\"text-white/70 hover:text-white drop-shadow-sm\">Features</Link></li>\n              <li><Link href=\"/pricing\" className=\"text-white/70 hover:text-white drop-shadow-sm\">Pricing</Link></li>\n              <li><Link href=\"/provider-app\" className=\"text-white/70 hover:text-white drop-shadow-sm\">For Providers</Link></li>\n              <li><Link href=\"/apply\" className=\"text-white/70 hover:text-white drop-shadow-sm\">Apply</Link></li>\n            </ul>\n          </div>\n          <div>\n            <h3 className=\"font-semibold mb-4 text-white drop-shadow-lg\">Company</h3>\n            <ul className=\"space-y-2 text-sm\">\n              <li><Link href=\"/about\" className=\"text-white/70 hover:text-white drop-shadow-sm\">About Us</Link></li>\n              <li><Link href=\"/contact\" className=\"text-white/70 hover:text-white drop-shadow-sm\">Contact Us</Link></li>\n            </ul>\n          </div>\n          <div>\n            <h3 className=\"font-semibold mb-4 text-white drop-shadow-lg\">Legal</h3>\n            <ul className=\"space-y-2 text-sm\">\n              <li><Link href=\"/privacy\" className=\"text-white/70 hover:text-white drop-shadow-sm\">Privacy Policy</Link></li>\n              <li><Link href=\"/terms\" className=\"text-white/70 hover:text-white drop-shadow-sm\">Terms of Service</Link></li>\n            </ul>\n          </div>\n        </div>\n        <div className=\"mt-10 border-t border-border pt-8 text-center text-white/60 text-xs drop-shadow-sm\">\n          <p>© {new Date().getFullYear()} Vierla, Inc. All rights reserved.</p>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAmD;;;;;;8CACjE,8OAAC;oCAAE,WAAU;8CAAuC;;;;;;;;;;;;sCAItD,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA+C;;;;;;8CAC7D,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;0DAAgD;;;;;;;;;;;sDACrF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAgD;;;;;;;;;;;sDACpF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAgB,WAAU;0DAAgD;;;;;;;;;;;sDACzF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAgD;;;;;;;;;;;;;;;;;;;;;;;sCAGtF,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA+C;;;;;;8CAC7D,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAgD;;;;;;;;;;;sDAClF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAgD;;;;;;;;;;;;;;;;;;;;;;;sCAGxF,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA+C;;;;;;8CAC7D,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAgD;;;;;;;;;;;sDACpF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAIxF,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;;4BAAE;4BAAG,IAAI,OAAO,WAAW;4BAAG;;;;;;;;;;;;;;;;;;;;;;;AAKzC", "debugId": null}}, {"offset": {"line": 913, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/app/layout.tsx"], "sourcesContent": ["import type { Metada<PERSON> } from \"next\";\nimport { <PERSON>eist, <PERSON><PERSON>st_Mono } from \"next/font/google\";\nimport \"./globals.css\";\nimport { Navbar } from \"@/components/marketing/navbar\";\nimport { Footer } from \"@/components/marketing/footer\";\n\nconst geistSans = Geist({\n  variable: \"--font-geist-sans\",\n  subsets: [\"latin\"],\n});\n\nconst geistMono = Geist_Mono({\n  variable: \"--font-geist-mono\",\n  subsets: [\"latin\"],\n});\n\nexport const metadata: Metadata = {\n  title: \"Vierla - The AI-Powered Platform for Modern Business\",\n  description: \"Build, manage, and grow your business with our all-in-one platform featuring AI website builder, smart invoicing, integrated CRM, and actionable analytics.\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\">\n      <body\n        className={`${geistSans.variable} ${geistMono.variable} antialiased`}\n      >\n        <Navbar />\n        <main className=\"min-h-screen pt-20\">\n          {children}\n        </main>\n        <Footer />\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;;;;;;;AAYO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;YACC,WAAW,GAAG,yIAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,8IAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,YAAY,CAAC;;8BAEpE,8OAAC,kIAAA,CAAA,SAAM;;;;;8BACP,8OAAC;oBAAK,WAAU;8BACb;;;;;;8BAEH,8OAAC,kIAAA,CAAA,SAAM;;;;;;;;;;;;;;;;AAIf", "debugId": null}}]}